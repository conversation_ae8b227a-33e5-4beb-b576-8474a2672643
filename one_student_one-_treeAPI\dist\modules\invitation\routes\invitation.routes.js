"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const invitation_controller_1 = require("../controllers/invitation.controller");
const auth_middleware_1 = require("../../../middleware/auth.middleware");
const router = (0, express_1.Router)();
router.get('/validate/:token', invitation_controller_1.invitationController.validateInvitationToken);
router.post('/accept-public', invitation_controller_1.invitationController.acceptInvitationPublic);
router.use(auth_middleware_1.authenticate);
router.get('/', invitation_controller_1.invitationController.getInvitations);
router.post('/', invitation_controller_1.invitationController.createInvitation);
router.put('/:id', invitation_controller_1.invitationController.updateInvitation);
router.delete('/:id', invitation_controller_1.invitationController.deleteInvitation);
router.post('/:id/accept', invitation_controller_1.invitationController.acceptInvitation);
router.post('/:id/reject', invitation_controller_1.invitationController.rejectInvitation);
router.post('/:id/resend', invitation_controller_1.invitationController.resendInvitation);
exports.default = router;
//# sourceMappingURL=invitation.routes.js.map