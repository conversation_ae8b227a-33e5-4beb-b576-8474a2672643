"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDepartmentsByCollege = exports.deleteDepartment = exports.updateDepartment = exports.createDepartment = exports.getDepartmentById = exports.getDepartments = exports.getClassesByDepartmentPublic = exports.getDepartmentsByCollegePublic = exports.getDepartmentsPublic = void 0;
const department_service_1 = require("../services/department.service");
const department_repository_1 = require("../repositories/department.repository");
const user_repository_1 = require("../../user/repositories/user.repository");
const college_repository_1 = require("../../college/repositories/college.repository");
const data_utils_1 = require("../../../utils/data.utils");
const departmentService = new department_service_1.DepartmentService();
const getDepartmentsPublic = async (req, res) => {
    try {
        const result = await department_repository_1.departmentRepository.findAllWithDetails({
            limit: 1000,
            page: 1
        });
        const transformedResult = (0, data_utils_1.transformPaginatedResult)(result, data_utils_1.transformDepartment);
        res.status(200).json({
            success: true,
            message: 'Departments retrieved successfully',
            data: transformedResult.data,
            pagination: transformedResult.pagination,
        });
    }
    catch (error) {
        console.error('Get departments public error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve departments',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.getDepartmentsPublic = getDepartmentsPublic;
const getDepartmentsByCollegePublic = async (req, res) => {
    try {
        const { collegeId } = req.params;
        const result = await department_repository_1.departmentRepository.findByCollegeWithCounts(collegeId, {
            limit: 1000,
            page: 1
        });
        res.status(200).json({
            success: true,
            message: 'Departments retrieved successfully',
            data: result.data,
            pagination: result.pagination,
        });
    }
    catch (error) {
        console.error('Get departments by college public error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve departments',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.getDepartmentsByCollegePublic = getDepartmentsByCollegePublic;
const getClassesByDepartmentPublic = async (req, res) => {
    try {
        const { collegeId, departmentId } = req.params;
        const query = `
      SELECT DISTINCT class as name, class as id
      FROM users
      WHERE college_id = $1 AND department_id = $2 AND class IS NOT NULL AND class != ''
      ORDER BY class
    `;
        const result = await department_repository_1.departmentRepository.query(query, [collegeId, departmentId]);
        let classes = result.rows;
        if (classes.length === 0) {
            classes = [
                { id: 'CS-1A', name: 'CS-1A' },
                { id: 'CS-1B', name: 'CS-1B' },
                { id: 'CS-2A', name: 'CS-2A' },
                { id: 'CS-2B', name: 'CS-2B' },
                { id: 'CS-3A', name: 'CS-3A' },
                { id: 'CS-3B', name: 'CS-3B' },
                { id: 'CS-4A', name: 'CS-4A' },
                { id: 'CS-4B', name: 'CS-4B' }
            ];
        }
        res.status(200).json({
            success: true,
            message: 'Classes retrieved successfully',
            data: classes,
        });
    }
    catch (error) {
        console.error('Get classes by department public error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve classes',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.getClassesByDepartmentPublic = getClassesByDepartmentPublic;
const getDepartments = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required',
            });
            return;
        }
        const options = req.query;
        if (req.user.role === 'principal' && req.user.collegeId) {
            const result = await department_repository_1.departmentRepository.findByCollegeWithCounts(req.user.collegeId, options);
            const transformedResult = (0, data_utils_1.transformPaginatedResult)(result, data_utils_1.transformDepartment);
            res.status(200).json({
                success: true,
                message: 'Departments retrieved successfully',
                data: transformedResult.data,
                pagination: transformedResult.pagination,
            });
            return;
        }
        else if ((req.user.role === 'hod' || req.user.role === 'staff') && req.user.departmentId) {
            const department = await department_repository_1.departmentRepository.findByIdWithDetails(req.user.departmentId);
            if (!department) {
                res.status(404).json({
                    success: false,
                    message: 'Department not found',
                });
                return;
            }
            res.status(200).json({
                success: true,
                message: 'Department retrieved successfully',
                data: [(0, data_utils_1.transformDepartment)(department)],
                pagination: {
                    page: 1,
                    limit: 1,
                    total: 1,
                    totalPages: 1,
                },
            });
            return;
        }
        const result = await department_repository_1.departmentRepository.findAllWithDetails(options);
        const transformedResult = (0, data_utils_1.transformPaginatedResult)(result, data_utils_1.transformDepartment);
        res.status(200).json({
            success: true,
            message: 'Departments retrieved successfully',
            data: transformedResult.data,
            pagination: transformedResult.pagination,
        });
    }
    catch (error) {
        console.error('Get departments error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve departments',
        });
    }
};
exports.getDepartments = getDepartments;
const getDepartmentById = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required',
            });
            return;
        }
        const { departmentId } = req.params;
        if (req.user.role !== 'admin' && req.user.role !== 'principal' && req.user.departmentId !== departmentId) {
            res.status(403).json({
                success: false,
                message: 'Access denied to this department',
            });
            return;
        }
        const department = await department_repository_1.departmentRepository.findByIdWithDetails(departmentId);
        if (!department) {
            res.status(404).json({
                success: false,
                message: 'Department not found',
            });
            return;
        }
        if (req.user.role === 'principal' && req.user.collegeId !== department.collegeId) {
            res.status(403).json({
                success: false,
                message: 'Access denied to this department',
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'Department retrieved successfully',
            data: department,
        });
    }
    catch (error) {
        console.error('Get department by ID error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve department',
        });
    }
};
exports.getDepartmentById = getDepartmentById;
const createDepartment = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required',
            });
            return;
        }
        if (!['admin', 'principal'].includes(req.user.role)) {
            res.status(403).json({
                success: false,
                message: 'Only administrators and principals can create departments',
            });
            return;
        }
        const departmentData = req.body;
        const college = await college_repository_1.collegeRepository.findById(departmentData.collegeId);
        if (!college) {
            res.status(400).json({
                success: false,
                message: 'College not found',
            });
            return;
        }
        if (req.user.role === 'principal' && req.user.collegeId !== departmentData.collegeId) {
            res.status(403).json({
                success: false,
                message: 'You can only create departments in your college',
            });
            return;
        }
        const existingDepartment = await department_repository_1.departmentRepository.findByCodeAndCollege(departmentData.code, departmentData.collegeId);
        if (existingDepartment) {
            res.status(400).json({
                success: false,
                message: 'Department code already exists in this college',
            });
            return;
        }
        if (departmentData.hodId) {
            const hod = await user_repository_1.userRepository.findById(departmentData.hodId);
            if (!hod) {
                res.status(400).json({
                    success: false,
                    message: 'HOD not found',
                });
                return;
            }
            if (hod.role !== 'hod') {
                res.status(400).json({
                    success: false,
                    message: 'User is not a HOD',
                });
                return;
            }
            const existingAssignment = await department_repository_1.departmentRepository.findByHODId(departmentData.hodId);
            if (existingAssignment) {
                res.status(400).json({
                    success: false,
                    message: 'HOD is already assigned to another department',
                });
                return;
            }
        }
        const newDepartment = await department_repository_1.departmentRepository.createDepartment(departmentData);
        if (departmentData.hodId) {
            await user_repository_1.userRepository.updateUser(departmentData.hodId, {
                collegeId: departmentData.collegeId,
                departmentId: newDepartment.id,
            });
        }
        res.status(201).json({
            success: true,
            message: 'Department created successfully',
            data: newDepartment,
        });
    }
    catch (error) {
        console.error('Create department error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create department',
        });
    }
};
exports.createDepartment = createDepartment;
const updateDepartment = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required',
            });
            return;
        }
        const { departmentId } = req.params;
        const updateData = req.body;
        if (req.user.role !== 'admin' && req.user.role !== 'principal' && req.user.departmentId !== departmentId) {
            res.status(403).json({
                success: false,
                message: 'Access denied to update this department',
            });
            return;
        }
        const currentDepartment = await department_repository_1.departmentRepository.findById(departmentId);
        if (!currentDepartment) {
            res.status(404).json({
                success: false,
                message: 'Department not found',
            });
            return;
        }
        if (req.user.role === 'principal' && req.user.collegeId !== currentDepartment.collegeId) {
            res.status(403).json({
                success: false,
                message: 'Access denied to update this department',
            });
            return;
        }
        if (req.user.role === 'hod') {
            const allowedFields = ['name', 'totalStudents', 'totalStaff'];
            const updateFields = Object.keys(updateData);
            const hasUnallowedFields = updateFields.some(field => !allowedFields.includes(field));
            if (hasUnallowedFields) {
                res.status(403).json({
                    success: false,
                    message: 'HODs can only update name, total students, and total staff',
                });
                return;
            }
        }
        if (updateData.code) {
            const existingDepartment = await department_repository_1.departmentRepository.findByCodeAndCollege(updateData.code, currentDepartment.collegeId);
            if (existingDepartment && existingDepartment.id !== departmentId) {
                res.status(400).json({
                    success: false,
                    message: 'Department code already exists in this college',
                });
                return;
            }
        }
        const updatedDepartment = await department_repository_1.departmentRepository.updateDepartment(departmentId, updateData);
        if (!updatedDepartment) {
            res.status(404).json({
                success: false,
                message: 'Department not found',
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'Department updated successfully',
            data: updatedDepartment,
        });
    }
    catch (error) {
        console.error('Update department error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update department',
        });
    }
};
exports.updateDepartment = updateDepartment;
const deleteDepartment = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required',
            });
            return;
        }
        if (req.user.role !== 'admin') {
            res.status(403).json({
                success: false,
                message: 'Only administrators can delete departments',
            });
            return;
        }
        const { departmentId } = req.params;
        const deleted = await department_repository_1.departmentRepository.delete(departmentId);
        if (!deleted) {
            res.status(404).json({
                success: false,
                message: 'Department not found',
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'Department deleted successfully',
        });
    }
    catch (error) {
        console.error('Delete department error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete department',
        });
    }
};
exports.deleteDepartment = deleteDepartment;
const getDepartmentsByCollege = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required',
            });
            return;
        }
        const { collegeId } = req.params;
        const options = req.query;
        if (req.user.role !== 'admin' && req.user.collegeId !== collegeId) {
            res.status(403).json({
                success: false,
                message: 'Access denied to this college',
            });
            return;
        }
        const result = await department_repository_1.departmentRepository.findByCollegeWithCounts(collegeId, options);
        res.status(200).json({
            success: true,
            message: 'Departments retrieved successfully',
            data: result.data,
            pagination: result.pagination,
        });
    }
    catch (error) {
        console.error('Get departments by college error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve departments',
        });
    }
};
exports.getDepartmentsByCollege = getDepartmentsByCollege;
//# sourceMappingURL=department.controller.js.map