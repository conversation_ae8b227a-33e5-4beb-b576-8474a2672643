import axios from 'axios';
import { User, College, Department, Invitation, RegistrationRequest } from '../types';

const API_BASE_URL = 'http://localhost:3000/api/v1';

// Utility function to convert snake_case to camelCase
const toCamelCase = (str: string): string => {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
};

// Utility function to convert object keys from snake_case to camelCase
const convertKeysToCamelCase = (obj: any): any => {
  if (obj === null || obj === undefined) return obj;
  if (Array.isArray(obj)) return obj.map(convertKeysToCamelCase);
  if (obj instanceof Date) return obj; // Preserve Date objects
  if (typeof obj !== 'object') return obj;

  const converted: any = {};
  for (const [key, value] of Object.entries(obj)) {
    const camelKey = toCamelCase(key);
    converted[camelKey] = convertKeysToCamelCase(value);
  }
  return converted;
};

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle auth errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear auth data and redirect to login
      localStorage.removeItem('authToken');
      localStorage.removeItem('currentUser');
      window.location.href = '/';
    }
    return Promise.reject(error);
  }
);

// API Service class
export class ApiService {
  // ==================== AUTH ENDPOINTS ====================
  static async login(email: string, password: string) {
    const response = await apiClient.post('/auth/login', { email, password });
    return response.data;
  }

  static async logout() {
    const response = await apiClient.post('/auth/logout');
    return response.data;
  }

  static async checkAuth() {
    const response = await apiClient.get('/auth/check');
    return response.data;
  }

  // ==================== USER ENDPOINTS ====================
  static async getUsers(): Promise<User[]> {
    const response = await apiClient.get('/users');
    return convertKeysToCamelCase(response.data.data);
  }

  static async createUser(userData: any): Promise<User> {
    const response = await apiClient.post('/users', userData);
    return convertKeysToCamelCase(response.data.data);
  }

  static async updateUser(userId: string, userData: any): Promise<User> {
    const response = await apiClient.put(`/users/${userId}`, userData);
    return convertKeysToCamelCase(response.data.data);
  }

  static async deleteUser(userId: string): Promise<void> {
    await apiClient.delete(`/users/${userId}`);
  }

  static async getUserById(id: string): Promise<User> {
    const response = await apiClient.get(`/users/${id}`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async updateProfile(profileData: any): Promise<User> {
    const response = await apiClient.put('/auth/profile', profileData);
    return convertKeysToCamelCase(response.data.data);
  }

  static async changePassword(userId: string, passwordData: any): Promise<void> {
    await apiClient.put(`/users/${userId}/password`, passwordData);
  }

  // ==================== COLLEGE ENDPOINTS ====================
  static async getColleges(): Promise<College[]> {
    const response = await apiClient.get('/colleges');
    return convertKeysToCamelCase(response.data.data);
  }

  static async getCollegesPublic(): Promise<College[]> {
    const response = await apiClient.get('/colleges/public');
    return convertKeysToCamelCase(response.data.data);
  }

  static async createCollege(collegeData: any): Promise<College> {
    const response = await apiClient.post('/colleges', collegeData);
    return convertKeysToCamelCase(response.data.data);
  }

  static async updateCollege(collegeId: string, collegeData: any): Promise<College> {
    const response = await apiClient.put(`/colleges/${collegeId}`, collegeData);
    return convertKeysToCamelCase(response.data.data);
  }

  static async deleteCollege(collegeId: string): Promise<void> {
    await apiClient.delete(`/colleges/${collegeId}`);
  }

  static async getCollegeById(id: string): Promise<College> {
    const response = await apiClient.get(`/colleges/${id}`);
    return convertKeysToCamelCase(response.data.data);
  }

  // ==================== DEPARTMENT ENDPOINTS ====================
  static async getDepartments(): Promise<Department[]> {
    const response = await apiClient.get('/departments');
    return convertKeysToCamelCase(response.data.data);
  }

  static async getDepartmentsPublic(): Promise<Department[]> {
    const response = await apiClient.get('/departments/public');
    return convertKeysToCamelCase(response.data.data);
  }

  static async getDepartmentsByCollegePublic(collegeId: string): Promise<Department[]> {
    const response = await apiClient.get(`/departments/public/college/${collegeId}`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async getClassesByDepartmentPublic(collegeId: string, departmentId: string): Promise<{id: string, name: string}[]> {
    const response = await apiClient.get(`/departments/public/classes/${collegeId}/${departmentId}`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async createDepartment(departmentData: any): Promise<Department> {
    const response = await apiClient.post('/departments', departmentData);
    return convertKeysToCamelCase(response.data.data);
  }

  static async updateDepartment(departmentId: string, departmentData: any): Promise<Department> {
    const response = await apiClient.put(`/departments/${departmentId}`, departmentData);
    return convertKeysToCamelCase(response.data.data);
  }

  static async deleteDepartment(departmentId: string): Promise<void> {
    await apiClient.delete(`/departments/${departmentId}`);
  }

  static async getDepartmentById(id: string): Promise<Department> {
    const response = await apiClient.get(`/departments/${id}`);
    return convertKeysToCamelCase(response.data.data);
  }

  // ==================== INVITATION ENDPOINTS ====================
  static async getInvitations(): Promise<Invitation[]> {
    const response = await apiClient.get('/invitations');
    return convertKeysToCamelCase(response.data.data);
  }

  static async createInvitation(invitationData: any): Promise<Invitation> {
    const response = await apiClient.post('/invitations', invitationData);
    return convertKeysToCamelCase(response.data.data);
  }

  static async updateInvitation(invitationId: string, invitationData: any): Promise<Invitation> {
    const response = await apiClient.put(`/invitations/${invitationId}`, invitationData);
    return convertKeysToCamelCase(response.data.data);
  }

  static async deleteInvitation(invitationId: string): Promise<void> {
    await apiClient.delete(`/invitations/${invitationId}`);
  }

  static async acceptInvitation(invitationId: string): Promise<Invitation> {
    const response = await apiClient.post(`/invitations/${invitationId}/accept`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async rejectInvitation(invitationId: string): Promise<Invitation> {
    const response = await apiClient.post(`/invitations/${invitationId}/reject`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async resendInvitation(invitationId: string): Promise<Invitation> {
    const response = await apiClient.post(`/invitations/${invitationId}/resend`);
    return convertKeysToCamelCase(response.data.data);
  }

  // Content Management APIs
  static async getGuidelines(): Promise<any[]> {
    const response = await apiClient.get('/content/guidelines');
    return response.data.data;
  }

  static async createGuideline(data: any): Promise<any> {
    const response = await apiClient.post('/content/guidelines', data);
    return response.data.data;
  }

  static async updateGuideline(id: string, data: any): Promise<any> {
    const response = await apiClient.put(`/content/guidelines/${id}`, data);
    return response.data.data;
  }

  static async deleteGuideline(id: string): Promise<void> {
    await apiClient.delete(`/content/guidelines/${id}`);
  }

  static async getResources(): Promise<any[]> {
    const response = await apiClient.get('/content/resources');
    return response.data.data;
  }

  static async createResource(data: any): Promise<any> {
    const response = await apiClient.post('/content/resources', data);
    return response.data.data;
  }

  static async updateResource(id: string, data: any): Promise<any> {
    const response = await apiClient.put(`/content/resources/${id}`, data);
    return response.data.data;
  }

  static async deleteResource(id: string): Promise<void> {
    await apiClient.delete(`/content/resources/${id}`);
  }

  // Public invitation endpoints (no authentication required)
  static async validateInvitationToken(token: string): Promise<Invitation> {
    const response = await axios.get(`${API_BASE_URL}/invitations/validate/${token}`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async acceptInvitationPublic(invitationData: {
    invitationToken: string;
    name: string;
    phone: string;
    password: string;
  }): Promise<any> {
    const response = await axios.post(`${API_BASE_URL}/invitations/accept-public`, invitationData);
    return convertKeysToCamelCase(response.data.data);
  }

  // ==================== REGISTRATION REQUEST ENDPOINTS ====================
  static async getRegistrationRequests(): Promise<RegistrationRequest[]> {
    const response = await apiClient.get('/registration-requests');
    return convertKeysToCamelCase(response.data.data);
  }

  static async createRegistrationRequest(requestData: any): Promise<RegistrationRequest> {
    const response = await apiClient.post('/registration-requests', requestData);
    return convertKeysToCamelCase(response.data.data);
  }

  static async updateRegistrationRequest(requestId: string, requestData: any): Promise<RegistrationRequest> {
    const response = await apiClient.put(`/registration-requests/${requestId}`, requestData);
    return convertKeysToCamelCase(response.data.data);
  }

  static async approveRegistrationRequest(requestId: string): Promise<User> {
    const response = await apiClient.post(`/registration-requests/${requestId}/approve`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async rejectRegistrationRequest(requestId: string, reason?: string): Promise<void> {
    await apiClient.post(`/registration-requests/${requestId}/reject`, { reason });
  }

  // ==================== DASHBOARD ENDPOINTS ====================
  static async getDashboardOverview() {
    const response = await apiClient.get('/dashboard/overview');
    return convertKeysToCamelCase(response.data.data);
  }

  static async getRecentActivity() {
    try {
      const response = await apiClient.get('/dashboard/activity');
      return convertKeysToCamelCase(response.data.data);
    } catch (error) {
      console.warn('Recent activity endpoint not available');
      return [];
    }
  }

  // ==================== TREE ENDPOINTS ====================
  static async getTrees(): Promise<any[]> {
    const response = await apiClient.get('/trees');
    return convertKeysToCamelCase(response.data.data);
  }

  static async createTree(treeData: any): Promise<any> {
    const response = await apiClient.post('/trees', treeData);
    return convertKeysToCamelCase(response.data.data);
  }

  static async updateTree(treeId: string, treeData: any): Promise<any> {
    const response = await apiClient.put(`/trees/${treeId}`, treeData);
    return convertKeysToCamelCase(response.data.data);
  }

  static async deleteTree(treeId: string): Promise<void> {
    await apiClient.delete(`/trees/${treeId}`);
  }

  static async getAvailableTrees(): Promise<any[]> {
    const response = await apiClient.get('/tree-selection/available');
    return convertKeysToCamelCase(response.data.data);
  }

  static async getMyTreeSelection(): Promise<any> {
    try {
      // Use tree selection endpoint which has the correct tree assignment data
      const response = await apiClient.get('/tree-selection/my-selection');
      return convertKeysToCamelCase(response.data.data);
    } catch (error) {
      console.error('Failed to get tree selection:', error);
      return null;
    }
  }

  static async selectTree(treeId: string): Promise<any> {
    const response = await apiClient.post('/tree-selection/select', { treeId });
    return convertKeysToCamelCase(response.data.data);
  }

  static async markTreeAsPlanted(selectionId: string, plantingImageId?: string): Promise<any> {
    const response = await apiClient.put('/tree-selection/mark-planted', { selectionId, plantingImageId });
    return convertKeysToCamelCase(response.data.data);
  }

  static async getStudentTreeSelection(): Promise<any> {
    const response = await apiClient.get('/tree-selection/my-selection');
    return convertKeysToCamelCase(response.data.data);
  }

  static async getTreeSelections(): Promise<any[]> {
    const response = await apiClient.get('/tree-selection');
    return convertKeysToCamelCase(response.data.data);
  }

  // ==================== UPLOAD ENDPOINTS ====================
  static async uploadTreeImage(treeId: string, file: File, photoType: string = 'progress', caption?: string): Promise<any> {
    const formData = new FormData();
    formData.append('image', file);
    formData.append('treeId', treeId);
    formData.append('photoType', photoType);
    if (caption) {
      formData.append('caption', caption);
    }

    // Create a separate axios instance for file uploads to avoid Content-Type conflicts
    const token = localStorage.getItem('authToken');
    const uploadResponse = await axios.post(`${API_BASE_URL}/uploads/tree-images`, formData, {
      headers: {
        'Authorization': token ? `Bearer ${token}` : '',
        // Don't set Content-Type - let browser set it automatically for FormData
      },
    });

    return convertKeysToCamelCase(uploadResponse.data.data);
  }

  // ==================== BULK UPLOAD ENDPOINTS ====================
  static async downloadBulkUploadTemplate(): Promise<Blob> {
    const response = await apiClient.get('/bulk-upload/colleges/template', {
      responseType: 'blob'
    });
    return response.data;
  }

  static async uploadCollegesCSV(file: File): Promise<any> {
    const formData = new FormData();
    formData.append('csvFile', file);

    const token = localStorage.getItem('authToken');
    const response = await axios.post(`${API_BASE_URL}/bulk-upload/colleges`, formData, {
      headers: {
        'Authorization': token ? `Bearer ${token}` : '',
      },
    });
    return convertKeysToCamelCase(response.data);
  }

  static async downloadStaffHODUploadTemplate(): Promise<Blob> {
    const response = await apiClient.get('/bulk-upload/staff-hod/template', {
      responseType: 'blob'
    });
    return response.data;
  }

  static async uploadStaffHODCSV(file: File): Promise<any> {
    const formData = new FormData();
    formData.append('csvFile', file);

    const token = localStorage.getItem('authToken');
    const response = await axios.post(`${API_BASE_URL}/bulk-upload/staff-hod`, formData, {
      headers: {
        'Authorization': token ? `Bearer ${token}` : '',
      },
    });
    return convertKeysToCamelCase(response.data);
  }

  static async downloadStudentUploadTemplate(): Promise<Blob> {
    const response = await apiClient.get('/bulk-upload/students/template', {
      responseType: 'blob'
    });
    return response.data;
  }

  static async uploadStudentsCSV(file: File): Promise<any> {
    const formData = new FormData();
    formData.append('csvFile', file);

    const token = localStorage.getItem('authToken');
    const response = await axios.post(`${API_BASE_URL}/bulk-upload/students`, formData, {
      headers: {
        'Authorization': token ? `Bearer ${token}` : '',
      },
    });
    return convertKeysToCamelCase(response.data);
  }

  static async getTreeImages(treeId: string): Promise<any[]> {
    const response = await apiClient.get(`/uploads/tree-images/${treeId}`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async deleteTreeImage(imageId: string): Promise<void> {
    await apiClient.delete(`/uploads/tree-images/${imageId}`);
  }

  // ==================== FILTERED DATA METHODS ====================
  static async getUsersByRole(role: string): Promise<User[]> {
    const response = await apiClient.get(`/users?role=${role}`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async getUsersByCollege(collegeId: string): Promise<User[]> {
    const response = await apiClient.get(`/users?collegeId=${collegeId}`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async getUsersByDepartment(departmentId: string): Promise<User[]> {
    const response = await apiClient.get(`/users?departmentId=${departmentId}`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async getDepartmentsByCollege(collegeId: string): Promise<Department[]> {
    const response = await apiClient.get(`/departments?collegeId=${collegeId}`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async getTreesByCollege(collegeId: string): Promise<any[]> {
    const response = await apiClient.get(`/trees?collegeId=${collegeId}`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async getTreesByDepartment(departmentId: string): Promise<any[]> {
    const response = await apiClient.get(`/trees?departmentId=${departmentId}`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async getTreesByStudent(studentId: string): Promise<any[]> {
    const response = await apiClient.get(`/trees?assignedStudentId=${studentId}`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async getRegistrationRequestsByCollege(collegeId: string): Promise<RegistrationRequest[]> {
    const response = await apiClient.get(`/registration-requests?collegeId=${collegeId}`);
    return convertKeysToCamelCase(response.data.data);
  }

  static async getInvitationsByCollege(collegeId: string): Promise<Invitation[]> {
    const response = await apiClient.get(`/invitations?collegeId=${collegeId}`);
    return convertKeysToCamelCase(response.data.data);
  }
}

export default ApiService;
