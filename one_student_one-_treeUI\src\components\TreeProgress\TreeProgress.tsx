import React, { useState, useEffect } from 'react';
import {
  Camera,
  Calendar,
  TrendingUp,
  Heart,
  Eye,
  Award
} from 'lucide-react';

import { ApiService } from '../../services/api';
import Card, { StatCard } from '../UI/Card';
import LoadingSpinner from '../UI/LoadingSpinner';

interface TreeImage {
  id: string;
  imageUrl: string;
  uploadDate: string;
  description: string;
  measurements?: {
    height: number;
    diameter: number;
  };
}

interface TreeProgress {
  tree: {
    id: string;
    species: string;
    treeCode: string;
    plantedDate: string;
    locationDescription: string;
  };
  images: TreeImage[];
  totalImages: number;
  lastUpdate: string;
  healthStatus: 'excellent' | 'good' | 'fair' | 'poor';
  growthRate: number;
}

const TreeProgress: React.FC = () => {
  const [progress, setProgress] = useState<TreeProgress | null>(null);
  const [loading, setLoading] = useState(true);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [selectedImage, setSelectedImage] = useState<TreeImage | null>(null);

  useEffect(() => {
    loadProgress();
  }, []);

  const loadProgress = async () => {
    try {
      // Try to get tree selection first
      let treeData = null;
      let images: any[] = [];

      try {
        const selection = await ApiService.getMyTreeSelection();
        if (selection && selection.tree_id) {
          // Get tree details from the trees endpoint
          const userStr = localStorage.getItem('user');
          if (userStr) {
            const user = JSON.parse(userStr);
            const trees = await ApiService.getTreesByStudent(user.id);
            treeData = trees.find(t => t.id === selection.tree_id) || trees[0];
          }
        }
      } catch (selectionError) {
        console.log('No tree selection found, trying assigned trees:', selectionError);
      }

      // If no selection, get assigned trees directly
      if (!treeData) {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          const user = JSON.parse(userStr);
          const trees = await ApiService.getTreesByStudent(user.id);
          treeData = trees[0]; // Get first assigned tree
        }
      }

      // Load images if we have tree data
      if (treeData && treeData.id) {
        try {
          images = await ApiService.getTreeImages(treeData.id);
        } catch (imageError) {
          console.log('No images found for tree:', imageError);
          images = [];
        }
      }

      if (treeData) {
        setProgress({
          tree: treeData,
          images: images || [],
          totalImages: images?.length || 0,
          lastUpdate: images?.[0]?.uploadDate || images?.[0]?.createdAt || treeData.plantedDate || treeData.planted_date,
          healthStatus: treeData.status === 'healthy' ? 'good' : 'fair',
          growthRate: 15 // This would be calculated from monitoring data
        });
      }
    } catch (error) {
      console.error('Failed to load progress:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = async (file: File, description: string) => {
    if (!progress || !progress.tree) return;

    setUploadingImage(true);
    try {
      await ApiService.uploadTreeImage(progress.tree.id, file, 'progress', description);
      await loadProgress(); // Reload to get updated data
      setShowUploadModal(false);
    } catch (error) {
      console.error('Failed to upload image:', error);
      alert('Failed to upload image. Please try again.');
    } finally {
      setUploadingImage(false);
    }
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600';
      case 'good': return 'text-blue-600';
      case 'fair': return 'text-yellow-600';
      case 'poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getDaysPlanted = () => {
    if (!progress || !progress.tree || !progress.tree.plantedDate) return 0;
    const planted = new Date(progress.tree.plantedDate);
    const now = new Date();
    return Math.floor((now.getTime() - planted.getTime()) / (1000 * 60 * 60 * 24));
  };

  if (loading) {
    return (
      <div className="p-6">
        <LoadingSpinner size="lg" text="Loading your tree progress..." />
      </div>
    );
  }

  if (!progress) {
    return (
      <div className="p-6">
        <Card className="text-center py-12">
          <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Tree Assigned</h3>
          <p className="text-gray-500 mb-4">You haven't selected a tree yet.</p>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
            Select a Tree
          </button>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Heart className="mr-3 text-red-500" />
            My Tree Progress
          </h1>
          <p className="text-gray-600 mt-2">
            Track the growth and health of your {progress.tree?.species || 'tree'}
          </p>
        </div>
        
        <button
          onClick={() => setShowUploadModal(true)}
          className="mt-4 lg:mt-0 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors flex items-center"
        >
          <Camera className="w-5 h-5 mr-2" />
          Upload Progress Photo
        </button>
      </div>

      {/* Tree Info Card */}
      <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-green-800">{progress.tree?.species || 'Unknown Species'}</h2>
            <p className="text-green-600">{progress.tree?.treeCode || 'No Code'}</p>
            <p className="text-sm text-gray-600 mt-1">{progress.tree?.locationDescription || 'Location not specified'}</p>
          </div>
          <div className="text-right">
            <div className={`text-lg font-semibold ${getHealthColor(progress.healthStatus)}`}>
              {progress.healthStatus.charAt(0).toUpperCase() + progress.healthStatus.slice(1)}
            </div>
            <p className="text-sm text-gray-600">Health Status</p>
          </div>
        </div>
      </Card>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Days Planted"
          value={getDaysPlanted()}
          icon={Calendar}
          color="blue"
        />
        <StatCard
          title="Photos Uploaded"
          value={progress.totalImages}
          icon={Camera}
          color="green"
        />
        <StatCard
          title="Growth Rate"
          value={`${progress.growthRate}%`}
          icon={TrendingUp}
          color="purple"
          trend={{ value: '+5%', isPositive: true }}
        />
        <StatCard
          title="Care Score"
          value="85"
          icon={Award}
          color="yellow"
          trend={{ value: '+12', isPositive: true }}
        />
      </div>

      {/* Progress Timeline */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Progress Timeline</h3>
        
        {progress.images.length === 0 ? (
          <div className="text-center py-8">
            <Camera className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500">No progress photos yet</p>
            <p className="text-sm text-gray-400">Upload your first photo to start tracking!</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {progress.images.map((image, index) => (
              <div key={image.id} className="relative group">
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                  <img
                    src={image.imageUrl}
                    alt={`Progress ${index + 1}`}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                  />
                </div>
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                  <button
                    onClick={() => setSelectedImage(image)}
                    className="opacity-0 group-hover:opacity-100 bg-white text-gray-900 px-3 py-1 rounded-lg text-sm font-medium transition-opacity duration-200"
                  >
                    <Eye className="w-4 h-4 inline mr-1" />
                    View
                  </button>
                </div>
                <div className="mt-2">
                  <p className="text-sm font-medium text-gray-900">{image.description}</p>
                  <p className="text-xs text-gray-500">
                    {new Date(image.uploadDate).toLocaleDateString()}
                  </p>
                  {image.measurements && (
                    <div className="flex space-x-4 mt-1 text-xs text-gray-600">
                      <span>H: {image.measurements.height}cm</span>
                      <span>D: {image.measurements.diameter}cm</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>

      {/* Upload Modal */}
      {showUploadModal && (
        <UploadModal
          onClose={() => setShowUploadModal(false)}
          onUpload={handleImageUpload}
          uploading={uploadingImage}
        />
      )}

      {/* Image Viewer Modal */}
      {selectedImage && (
        <ImageViewerModal
          image={selectedImage}
          onClose={() => setSelectedImage(null)}
        />
      )}
    </div>
  );
};

// Upload Modal Component
interface UploadModalProps {
  onClose: () => void;
  onUpload: (file: File, description: string, measurements?: { height: number; diameter: number }) => void;
  uploading: boolean;
}

const UploadModal: React.FC<UploadModalProps> = ({ onClose, onUpload, uploading }) => {
  const [file, setFile] = useState<File | null>(null);
  const [description, setDescription] = useState('');
  const [height, setHeight] = useState('');
  const [diameter, setDiameter] = useState('');
  const [includeMeasurements, setIncludeMeasurements] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) return;

    const measurements = includeMeasurements && height && diameter ? {
      height: parseFloat(height),
      diameter: parseFloat(diameter)
    } : undefined;

    onUpload(file, description, measurements);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-md w-full p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Upload Progress Photo</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Image
            </label>
            <input
              type="file"
              accept="image/*"
              onChange={(e) => setFile(e.target.files?.[0] || null)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe the current state of your tree..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              rows={3}
              required
            />
          </div>
          
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={includeMeasurements}
                onChange={(e) => setIncludeMeasurements(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-gray-700">Include measurements</span>
            </label>
          </div>
          
          {includeMeasurements && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Height (cm)
                </label>
                <input
                  type="number"
                  value={height}
                  onChange={(e) => setHeight(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="0"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Diameter (cm)
                </label>
                <input
                  type="number"
                  value={diameter}
                  onChange={(e) => setDiameter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="0"
                />
              </div>
            </div>
          )}
          
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              disabled={uploading}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={uploading || !file}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {uploading ? 'Uploading...' : 'Upload'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Image Viewer Modal Component
interface ImageViewerModalProps {
  image: TreeImage;
  onClose: () => void;
}

const ImageViewerModal: React.FC<ImageViewerModalProps> = ({ image, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-2xl w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">Progress Photo</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>
        
        <div className="space-y-4">
          <img
            src={image.imageUrl}
            alt="Tree progress"
            className="w-full h-64 object-cover rounded-lg"
          />
          
          <div>
            <h3 className="font-medium text-gray-900">Description</h3>
            <p className="text-gray-600 mt-1">{image.description}</p>
          </div>
          
          <div className="flex justify-between text-sm text-gray-600">
            <span>Uploaded: {new Date(image.uploadDate).toLocaleDateString()}</span>
            {image.measurements && (
              <span>Height: {image.measurements.height}cm, Diameter: {image.measurements.diameter}cm</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TreeProgress;
