interface EmailOptions {
    to: string;
    subject: string;
    html: string;
    text?: string;
}
declare class EmailService {
    private transporter;
    private isConfigured;
    constructor();
    private initializeTransporter;
    sendEmail(options: EmailOptions): Promise<boolean>;
    generateWelcomeEmail(userName: string, userEmail: string, temporaryPassword: string, role: string): EmailOptions;
    generatePasswordResetEmail(userName: string, userEmail: string, resetToken: string): EmailOptions;
    generateApprovalNotificationEmail(approverName: string, approverEmail: string, requestorName: string, requestedRole: string, workflowId: string): EmailOptions;
    generatePrincipalInvitationEmail(principalName: string, principalEmail: string, collegeName: string, invitationToken: string, temporaryPassword?: string): EmailOptions;
    generateStaffHODInvitationEmail(staffName: string, staffEmail: string, role: string, departmentName: string, principalName: string, invitationToken: string, temporaryPassword?: string): EmailOptions;
    generateStudentInvitationEmail(studentName: string, studentEmail: string, rollNumber: string, year: string, section: string, departmentName: string, staffName: string, invitationToken: string, temporaryPassword?: string): EmailOptions;
}
export declare const emailService: EmailService;
export {};
//# sourceMappingURL=email.service.d.ts.map