"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCollegeStatistics = exports.deleteCollege = exports.updateCollege = exports.createCollege = exports.getCollegeById = exports.getColleges = exports.getCollegesPublic = void 0;
const college_repository_1 = require("../repositories/college.repository");
const user_repository_1 = require("../../user/repositories/user.repository");
const data_utils_1 = require("../../../utils/data.utils");
const getCollegesPublic = async (req, res) => {
    try {
        const result = await college_repository_1.collegeRepository.findAllWithPrincipal({
            status: 'active',
            limit: 1000,
            page: 1
        });
        const transformedResult = (0, data_utils_1.transformPaginatedResult)(result, data_utils_1.transformCollege);
        res.status(200).json({
            success: true,
            message: 'Colleges retrieved successfully',
            data: transformedResult.data,
            pagination: transformedResult.pagination,
        });
    }
    catch (error) {
        console.error('Get colleges public error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve colleges',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.getCollegesPublic = getCollegesPublic;
const getColleges = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required',
            });
            return;
        }
        const options = req.query;
        if (req.user.role === 'principal' && req.user.collegeId) {
            const college = await college_repository_1.collegeRepository.findByIdWithPrincipal(req.user.collegeId);
            if (!college) {
                res.status(404).json({
                    success: false,
                    message: 'College not found',
                });
                return;
            }
            res.status(200).json({
                success: true,
                message: 'College retrieved successfully',
                data: [(0, data_utils_1.transformCollege)(college)],
                pagination: {
                    page: 1,
                    limit: 1,
                    total: 1,
                    totalPages: 1,
                },
            });
            return;
        }
        const result = await college_repository_1.collegeRepository.findAllWithPrincipal(options);
        const transformedResult = (0, data_utils_1.transformPaginatedResult)(result, data_utils_1.transformCollege);
        res.status(200).json({
            success: true,
            message: 'Colleges retrieved successfully',
            data: transformedResult.data,
            pagination: transformedResult.pagination,
        });
    }
    catch (error) {
        console.error('Get colleges error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve colleges',
        });
    }
};
exports.getColleges = getColleges;
const getCollegeById = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required',
            });
            return;
        }
        const { collegeId } = req.params;
        if (req.user.role !== 'admin' && req.user.collegeId !== collegeId) {
            res.status(403).json({
                success: false,
                message: 'Access denied to this college',
            });
            return;
        }
        const college = await college_repository_1.collegeRepository.findByIdWithPrincipal(collegeId);
        if (!college) {
            res.status(404).json({
                success: false,
                message: 'College not found',
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'College retrieved successfully',
            data: college,
        });
    }
    catch (error) {
        console.error('Get college by ID error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve college',
        });
    }
};
exports.getCollegeById = getCollegeById;
const createCollege = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required',
            });
            return;
        }
        if (req.user.role !== 'admin') {
            res.status(403).json({
                success: false,
                message: 'Only administrators can create colleges',
            });
            return;
        }
        const { principalName, principalEmail, principalPhone, ...collegeData } = req.body;
        const existingCollege = await college_repository_1.collegeRepository.findByEmail(collegeData.email);
        if (existingCollege) {
            res.status(400).json({
                success: false,
                message: 'College email already exists',
            });
            return;
        }
        let principalId = null;
        if (principalName && principalEmail) {
            const existingPrincipal = await user_repository_1.userRepository.findByEmail(principalEmail);
            if (existingPrincipal) {
                principalId = existingPrincipal.id;
            }
            else {
                try {
                    const { hashPassword } = require('../../../utils/auth.utils');
                    const tempPassword = 'TempPassword@123';
                    const passwordHash = await hashPassword(tempPassword);
                    const principalData = {
                        name: principalName,
                        email: principalEmail,
                        phone: principalPhone || null,
                        role: 'principal',
                        passwordHash: passwordHash,
                        status: 'pending'
                    };
                    const newPrincipal = await user_repository_1.userRepository.createUser(principalData);
                    principalId = newPrincipal.id;
                }
                catch (principalError) {
                    console.error(`Failed to create principal:`, principalError.message);
                }
            }
        }
        if (collegeData.principalId) {
            const principal = await user_repository_1.userRepository.findById(collegeData.principalId);
            if (!principal) {
                res.status(400).json({
                    success: false,
                    message: 'Principal not found',
                });
                return;
            }
            if (principal.role !== 'principal') {
                res.status(400).json({
                    success: false,
                    message: 'User is not a principal',
                });
                return;
            }
            const existingAssignment = await college_repository_1.collegeRepository.findByPrincipalId(collegeData.principalId);
            if (existingAssignment) {
                res.status(400).json({
                    success: false,
                    message: 'Principal is already assigned to another college',
                });
                return;
            }
            principalId = collegeData.principalId;
        }
        const finalCollegeData = {
            ...collegeData,
            principalId
        };
        const newCollege = await college_repository_1.collegeRepository.createCollege(finalCollegeData);
        if (principalId) {
            await user_repository_1.userRepository.updateUser(principalId, {
                collegeId: newCollege.id,
            });
            if (principalName && principalEmail) {
                try {
                    const invitationRepository = require('../../invitation/repositories/invitation.repository').invitationRepository;
                    const { emailService } = require('../../../utils/email.service');
                    const invitationToken = require('crypto').randomBytes(32).toString('hex');
                    const expiresAt = new Date();
                    expiresAt.setDate(expiresAt.getDate() + 7);
                    const invitation = await invitationRepository.createInvitation({
                        email: principalEmail,
                        role: 'principal',
                        sentBy: req.user.id,
                        collegeId: newCollege.id,
                        departmentId: null,
                        invitationToken: invitationToken,
                        expiresAt: expiresAt,
                        status: 'pending'
                    });
                    const temporaryPassword = 'TempPassword@123';
                    const emailOptions = emailService.generatePrincipalInvitationEmail(principalName, principalEmail, newCollege.name, invitation.invitationToken, temporaryPassword);
                    const emailSent = await emailService.sendEmail(emailOptions);
                    if (emailSent) {
                        console.log(`✅ Principal invitation email sent successfully to ${principalEmail}`);
                    }
                    else {
                        console.log(`❌ Failed to send principal invitation email to ${principalEmail}`);
                    }
                }
                catch (emailError) {
                    console.error('❌ Failed to send principal invitation:', emailError);
                }
            }
        }
        res.status(201).json({
            success: true,
            message: 'College created successfully',
            data: newCollege,
        });
    }
    catch (error) {
        console.error('Create college error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create college',
        });
    }
};
exports.createCollege = createCollege;
const updateCollege = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required',
            });
            return;
        }
        const { collegeId } = req.params;
        const updateData = req.body;
        if (req.user.role !== 'admin' && req.user.collegeId !== collegeId) {
            res.status(403).json({
                success: false,
                message: 'Access denied to update this college',
            });
            return;
        }
        if (req.user.role === 'principal') {
            const allowedFields = ['name', 'address', 'phone', 'website'];
            const updateFields = Object.keys(updateData);
            const hasUnallowedFields = updateFields.some(field => !allowedFields.includes(field));
            if (hasUnallowedFields) {
                res.status(403).json({
                    success: false,
                    message: 'Principals can only update name, address, phone, and website',
                });
                return;
            }
        }
        if (updateData.email) {
            const existingCollege = await college_repository_1.collegeRepository.findByEmail(updateData.email);
            if (existingCollege && existingCollege.id !== collegeId) {
                res.status(400).json({
                    success: false,
                    message: 'College email already exists',
                });
                return;
            }
        }
        const updatedCollege = await college_repository_1.collegeRepository.updateCollege(collegeId, updateData);
        if (!updatedCollege) {
            res.status(404).json({
                success: false,
                message: 'College not found',
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'College updated successfully',
            data: updatedCollege,
        });
    }
    catch (error) {
        console.error('Update college error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update college',
        });
    }
};
exports.updateCollege = updateCollege;
const deleteCollege = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required',
            });
            return;
        }
        if (req.user.role !== 'admin') {
            res.status(403).json({
                success: false,
                message: 'Only administrators can delete colleges',
            });
            return;
        }
        const { collegeId } = req.params;
        const deleted = await college_repository_1.collegeRepository.delete(collegeId);
        if (!deleted) {
            res.status(404).json({
                success: false,
                message: 'College not found',
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'College deleted successfully',
        });
    }
    catch (error) {
        console.error('Delete college error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete college',
        });
    }
};
exports.deleteCollege = deleteCollege;
const getCollegeStatistics = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required',
            });
            return;
        }
        if (req.user.role !== 'admin') {
            res.status(403).json({
                success: false,
                message: 'Only administrators can view college statistics',
            });
            return;
        }
        const statistics = await college_repository_1.collegeRepository.getStatistics();
        res.status(200).json({
            success: true,
            message: 'College statistics retrieved successfully',
            data: statistics,
        });
    }
    catch (error) {
        console.error('Get college statistics error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve college statistics',
        });
    }
};
exports.getCollegeStatistics = getCollegeStatistics;
//# sourceMappingURL=college.controller.js.map