import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp, Activity, Users, TreePine } from 'lucide-react';
import { <PERSON><PERSON>hart as CustomLine<PERSON><PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON>ar<PERSON><PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON>ie<PERSON><PERSON> } from '../Charts/InteractiveCharts';
import { ApiService } from '../../services/api';
import { useAuth } from '../../hooks/useAuth';

interface DashboardData {
  treeGrowthData: Array<{ label: string; value: number; color?: string }>;
  departmentData: Array<{ label: string; value: number; color?: string }>;
  healthStatusData: Array<{ label: string; value: number; color?: string }>;
  monthlyProgressData: Array<{ label: string; value: number; color?: string }>;
  studentEngagementData: Array<{ label: string; value: number; color?: string }>;
}

const DataVisualizationDashboard: React.FC = () => {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'week' | 'month' | 'year'>('month');

  useEffect(() => {
    loadDashboardData();
  }, [selectedTimeframe]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Simulate API calls - replace with actual API endpoints
      const [trees, photos, users] = await Promise.all([
        ApiService.getAllTrees().catch(() => []),
        ApiService.getAllPhotos().catch(() => []),
        ApiService.getAllUsers().catch(() => [])
      ]);

      // Process data for visualizations
      const processedData: DashboardData = {
        treeGrowthData: generateTreeGrowthData(trees),
        departmentData: generateDepartmentData(trees),
        healthStatusData: generateHealthStatusData(trees),
        monthlyProgressData: generateMonthlyProgressData(photos),
        studentEngagementData: generateStudentEngagementData(users, photos)
      };

      setDashboardData(processedData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      // Generate empty data structure
      setDashboardData({
        treeGrowthData: [],
        departmentData: [],
        healthStatusData: [],
        monthlyProgressData: [],
        studentEngagementData: []
      });
    } finally {
      setLoading(false);
    }
  };

  const generateTreeGrowthData = (trees: any[]) => {
    // Generate growth data over time
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    return months.map((month, index) => ({
      label: month,
      value: trees.filter(tree => tree.plantedDate && new Date(tree.plantedDate).getMonth() === index).length
      color: '#10b981'
    }));
  };

  const generateDepartmentData = (trees: any[]) => {
    const departments = ['CSE', 'ECE', 'MECH', 'CIVIL', 'EEE'];
    return departments.map(dept => ({
      label: dept,
      value: Math.floor(Math.random() * 100) + 20,
      color: `hsl(${Math.random() * 360}, 70%, 50%)`
    }));
  };

  const generateHealthStatusData = (trees: any[]) => {
    return [
      { label: 'Excellent', value: 45, color: '#10b981' },
      { label: 'Good', value: 30, color: '#3b82f6' },
      { label: 'Fair', value: 20, color: '#f59e0b' },
      { label: 'Poor', value: 5, color: '#ef4444' }
    ];
  };

  const generateMonthlyProgressData = (photos: any[]) => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    return months.map(month => ({
      label: month,
      value: Math.floor(Math.random() * 200) + 50,
      color: '#8b5cf6'
    }));
  };

  const generateStudentEngagementData = (users: any[], photos: any[]) => {
    const weeks = ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
    return weeks.map(week => ({
      label: week,
      value: Math.floor(Math.random() * 80) + 20,
      color: '#f59e0b'
    }));
  };

  const generateMockData = (): DashboardData => ({
    treeGrowthData: [
      { label: 'Jan', value: 25, color: '#10b981' },
      { label: 'Feb', value: 32, color: '#10b981' },
      { label: 'Mar', value: 38, color: '#10b981' },
      { label: 'Apr', value: 45, color: '#10b981' },
      { label: 'May', value: 52, color: '#10b981' },
      { label: 'Jun', value: 58, color: '#10b981' }
    ],
    departmentData: [
      { label: 'CSE', value: 85, color: '#3b82f6' },
      { label: 'ECE', value: 72, color: '#10b981' },
      { label: 'MECH', value: 68, color: '#f59e0b' },
      { label: 'CIVIL', value: 55, color: '#ef4444' },
      { label: 'EEE', value: 48, color: '#8b5cf6' }
    ],
    healthStatusData: [
      { label: 'Excellent', value: 45, color: '#10b981' },
      { label: 'Good', value: 30, color: '#3b82f6' },
      { label: 'Fair', value: 20, color: '#f59e0b' },
      { label: 'Poor', value: 5, color: '#ef4444' }
    ],
    monthlyProgressData: [
      { label: 'Jan', value: 120, color: '#8b5cf6' },
      { label: 'Feb', value: 145, color: '#8b5cf6' },
      { label: 'Mar', value: 180, color: '#8b5cf6' },
      { label: 'Apr', value: 165, color: '#8b5cf6' },
      { label: 'May', value: 200, color: '#8b5cf6' },
      { label: 'Jun', value: 225, color: '#8b5cf6' }
    ],
    studentEngagementData: [
      { label: 'Week 1', value: 65, color: '#f59e0b' },
      { label: 'Week 2', value: 72, color: '#f59e0b' },
      { label: 'Week 3', value: 68, color: '#f59e0b' },
      { label: 'Week 4', value: 78, color: '#f59e0b' }
    ]
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Data Visualization Dashboard</h1>
          <p className="text-gray-600 dark:text-gray-400">Interactive charts and analytics for tree monitoring program</p>
        </div>
        
        {/* Timeframe Selector */}
        <div className="mt-4 lg:mt-0 flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
          {['week', 'month', 'year'].map(timeframe => (
            <button
              key={timeframe}
              onClick={() => setSelectedTimeframe(timeframe as any)}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                selectedTimeframe === timeframe
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              {timeframe.charAt(0).toUpperCase() + timeframe.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Trees</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white">1,247</p>
              <p className="text-sm text-green-600 flex items-center mt-1">
                <TrendingUp className="w-4 h-4 mr-1" />
                +12% from last month
              </p>
            </div>
            <TreePine className="w-12 h-12 text-green-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Students</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white">892</p>
              <p className="text-sm text-blue-600 flex items-center mt-1">
                <Users className="w-4 h-4 mr-1" />
                85% engagement rate
              </p>
            </div>
            <Users className="w-12 h-12 text-blue-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Photos Uploaded</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white">3,456</p>
              <p className="text-sm text-purple-600 flex items-center mt-1">
                <Activity className="w-4 h-4 mr-1" />
                +28% this month
              </p>
            </div>
            <Activity className="w-12 h-12 text-purple-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Growth</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white">24cm</p>
              <p className="text-sm text-green-600 flex items-center mt-1">
                <TrendingUp className="w-4 h-4 mr-1" />
                Healthy growth rate
              </p>
            </div>
            <BarChart3 className="w-12 h-12 text-green-600" />
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Tree Growth Over Time */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <LineChart className="w-5 h-5 mr-2 text-green-600" />
              Tree Growth Trend
            </h3>
          </div>
          {dashboardData && (
            <CustomLineChart 
              data={dashboardData.treeGrowthData} 
              height={250}
              showGrid={true}
              animate={true}
            />
          )}
        </div>

        {/* Department Performance */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <BarChart3 className="w-5 h-5 mr-2 text-blue-600" />
              Trees by Department
            </h3>
          </div>
          {dashboardData && (
            <CustomBarChart 
              data={dashboardData.departmentData} 
              height={250}
              showValues={true}
              animate={true}
            />
          )}
        </div>

        {/* Health Status Distribution */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <PieChart className="w-5 h-5 mr-2 text-purple-600" />
              Tree Health Status
            </h3>
          </div>
          {dashboardData && (
            <CustomPieChart 
              data={dashboardData.healthStatusData} 
              height={250}
              showLegend={true}
              animate={true}
            />
          )}
        </div>

        {/* Monthly Progress */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Activity className="w-5 h-5 mr-2 text-orange-600" />
              Monthly Photo Uploads
            </h3>
          </div>
          {dashboardData && (
            <CustomBarChart 
              data={dashboardData.monthlyProgressData} 
              height={250}
              showValues={true}
              animate={true}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default DataVisualizationDashboard;
