import { Request, Response } from 'express';
export declare class ContentController {
    getGuidelines(req: Request, res: Response): Promise<void>;
    getResources(req: Request, res: Response): Promise<void>;
    createGuideline(req: Request, res: Response): Promise<void>;
    updateGuideline(req: Request, res: Response): Promise<void>;
    deleteGuideline(req: Request, res: Response): Promise<void>;
    createResource(req: Request, res: Response): Promise<void>;
    updateResource(req: Request, res: Response): Promise<void>;
    deleteResource(req: Request, res: Response): Promise<void>;
}
export declare const contentController: ContentController;
//# sourceMappingURL=content.controller.d.ts.map