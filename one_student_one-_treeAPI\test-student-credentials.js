const { pool } = require('./dist/config/database');
const axios = require('axios');

async function testStudentCredentials() {
  try {
    console.log('🔍 Finding student accounts...');
    
    // Get test students from database
    const result = await pool.query(`
      SELECT email, name, role
      FROM users
      WHERE role = 'student' AND (email LIKE '%test%' OR email LIKE '%demo%' OR email = '<EMAIL>')
      LIMIT 5
    `);
    
    console.log(`Found ${result.rows.length} students:`);
    result.rows.forEach(user => {
      console.log(`- ${user.name} (${user.email})`);
    });
    
    if (result.rows.length === 0) {
      console.log('❌ No students found');
      return;
    }
    
    // Test login with first student
    const testStudent = result.rows[0];
    console.log(`\n🧪 Testing login for: ${testStudent.email}`);
    
    const commonPasswords = [
      'Student@123',
      'password123',
      'Student@123456',
      'student123',
      '<PERSON><PERSON><PERSON><PERSON>@25',
      'Password123!',
      'test123',
      'demo123'
    ];
    
    for (const password of commonPasswords) {
      try {
        const response = await axios.post('http://localhost:3000/api/v1/auth/login', {
          email: testStudent.email,
          password: password
        });
        
        if (response.data.success) {
          console.log(`✅ Login successful!`);
          console.log(`📧 Email: ${testStudent.email}`);
          console.log(`🔑 Password: ${password}`);
          console.log(`👤 User: ${testStudent.name}`);
          
          // Test student dashboard access
          const token = response.data.data.token;
          const dashboardResponse = await axios.get('http://localhost:3000/api/v1/tree-selection/my-selection', {
            headers: { Authorization: `Bearer ${token}` }
          });
          
          console.log(`🌳 Tree assignment: ${dashboardResponse.data.data ? 'Has tree' : 'No tree assigned'}`);
          return;
        }
      } catch (error) {
        // Continue to next password
      }
    }
    
    console.log('❌ Could not login with any common password');
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    process.exit(0);
  }
}

testStudentCredentials();
