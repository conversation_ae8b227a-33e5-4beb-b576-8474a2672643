import React, { useState, useEffect } from 'react';
import { 
  Users, 
  TreePine, 
  Camera, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Filter,
  Search,
  Download,
  Eye,
  MessageSquare,
  Star,
  Award
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { ApiService } from '../../services/api';

interface StudentProgress {
  id: string;
  studentName: string;
  studentEmail: string;
  rollNumber: string;
  department: string;
  treeCode: string;
  treeSpecies: string;
  assignedDate: string;
  lastPhotoDate?: string;
  totalPhotos: number;
  currentHeight: number;
  growthRate: number;
  healthStatus: 'excellent' | 'good' | 'fair' | 'poor';
  careScore: number;
  isActive: boolean;
  notes?: string;
}

interface MonitoringFilters {
  department: string;
  healthStatus: string;
  dateRange: string;
  searchTerm: string;
}

const StudentProgressMonitoring: React.FC = () => {
  const { user } = useAuth();
  const [students, setStudents] = useState<StudentProgress[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<StudentProgress[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStudent, setSelectedStudent] = useState<StudentProgress | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [filters, setFilters] = useState<MonitoringFilters>({
    department: '',
    healthStatus: '',
    dateRange: '',
    searchTerm: ''
  });

  useEffect(() => {
    loadStudentProgress();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [students, filters]);

  const loadStudentProgress = async () => {
    try {
      setLoading(true);
      
      // Get students based on user role
      let studentsData;
      if (user?.role === 'staff') {
        studentsData = await ApiService.getMyStudents();
      } else if (user?.role === 'hod') {
        studentsData = await ApiService.getDepartmentStudents();
      } else {
        studentsData = await ApiService.getAllStudents();
      }

      // Process and enrich student data
      const enrichedData = await Promise.all(
        (studentsData || []).map(async (student: any) => {
          try {
            const [treeData, photos] = await Promise.all([
              ApiService.getStudentTree(student.id).catch(() => null),
              ApiService.getStudentPhotos(student.id).catch(() => [])
            ]);

            return {
              id: student.id,
              studentName: student.name,
              studentEmail: student.email,
              rollNumber: student.rollNumber || 'N/A',
              department: student.department?.name || 'Unknown',
              treeCode: treeData?.treeCode || 'Not Assigned',
              treeSpecies: treeData?.species || 'N/A',
              assignedDate: treeData?.assignedDate || student.createdAt,
              lastPhotoDate: photos[0]?.createdAt,
              totalPhotos: photos.length,
              currentHeight: photos[0]?.measurements?.height || 0,
              growthRate: calculateGrowthRate(photos),
              healthStatus: calculateHealthStatus(photos, treeData),
              careScore: calculateCareScore(photos, treeData),
              isActive: student.isActive,
              notes: student.notes
            };
          } catch (error) {
            console.error(`Error processing student ${student.id}:`, error);
            return null;
          }
        })
      );

      setStudents(enrichedData.filter(Boolean) as StudentProgress[]);
    } catch (error) {
      console.error('Failed to load student progress:', error);
      // Use mock data for demonstration
      setStudents(generateMockData());
    } finally {
      setLoading(false);
    }
  };

  const calculateGrowthRate = (photos: any[]) => {
    if (photos.length < 2) return 0;
    const latest = photos[0]?.measurements?.height || 0;
    const previous = photos[1]?.measurements?.height || 0;
    return latest > previous ? Math.round(((latest - previous) / previous) * 100) : 0;
  };

  const calculateHealthStatus = (photos: any[], treeData: any): 'excellent' | 'good' | 'fair' | 'poor' => {
    const photoCount = photos.length;
    const daysSinceAssignment = treeData ? 
      Math.floor((new Date().getTime() - new Date(treeData.assignedDate).getTime()) / (1000 * 60 * 60 * 24)) : 0;
    const expectedPhotos = Math.max(1, Math.floor(daysSinceAssignment / 30));
    
    const photoScore = photoCount / expectedPhotos;
    if (photoScore >= 1) return 'excellent';
    if (photoScore >= 0.7) return 'good';
    if (photoScore >= 0.4) return 'fair';
    return 'poor';
  };

  const calculateCareScore = (photos: any[], treeData: any) => {
    const photoScore = Math.min(100, photos.length * 10);
    const consistencyScore = photos.length > 0 ? 50 : 0;
    const timelinessScore = photos[0] ? 50 : 0;
    return Math.round((photoScore + consistencyScore + timelinessScore) / 3);
  };

  const generateMockData = (): StudentProgress[] => [
    {
      id: '1',
      studentName: 'Arjun Kumar',
      studentEmail: '<EMAIL>',
      rollNumber: '20CS001',
      department: 'Computer Science',
      treeCode: 'TREE001',
      treeSpecies: 'Neem',
      assignedDate: '2024-01-15',
      lastPhotoDate: '2024-01-28',
      totalPhotos: 8,
      currentHeight: 45,
      growthRate: 15,
      healthStatus: 'excellent',
      careScore: 92,
      isActive: true
    },
    {
      id: '2',
      studentName: 'Priya Sharma',
      studentEmail: '<EMAIL>',
      rollNumber: '20CS002',
      department: 'Computer Science',
      treeCode: 'TREE002',
      treeSpecies: 'Banyan',
      assignedDate: '2024-01-15',
      lastPhotoDate: '2024-01-25',
      totalPhotos: 6,
      currentHeight: 38,
      growthRate: 12,
      healthStatus: 'good',
      careScore: 78,
      isActive: true
    }
  ];

  const applyFilters = () => {
    let filtered = [...students];

    if (filters.department) {
      filtered = filtered.filter(s => s.department === filters.department);
    }

    if (filters.healthStatus) {
      filtered = filtered.filter(s => s.healthStatus === filters.healthStatus);
    }

    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(s => 
        s.studentName.toLowerCase().includes(term) ||
        s.rollNumber.toLowerCase().includes(term) ||
        s.treeCode.toLowerCase().includes(term)
      );
    }

    setFilteredStudents(filtered);
  };

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600 bg-green-100';
      case 'good': return 'text-blue-600 bg-blue-100';
      case 'fair': return 'text-yellow-600 bg-yellow-100';
      case 'poor': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent': return <CheckCircle className="w-4 h-4" />;
      case 'good': return <Star className="w-4 h-4" />;
      case 'fair': return <Clock className="w-4 h-4" />;
      case 'poor': return <AlertTriangle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Student Progress Monitoring</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor and track student tree care progress across your {user?.role === 'staff' ? 'assigned students' : 'department'}
          </p>
        </div>
        
        <div className="mt-4 lg:mt-0 flex items-center space-x-3">
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2">
            <Download className="w-4 h-4" />
            <span>Export Report</span>
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Students</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white">{students.length}</p>
            </div>
            <Users className="w-12 h-12 text-blue-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Trees</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white">
                {students.filter(s => s.treeCode !== 'Not Assigned').length}
              </p>
            </div>
            <TreePine className="w-12 h-12 text-green-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Care Score</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white">
                {Math.round(students.reduce((acc, s) => acc + s.careScore, 0) / students.length) || 0}
              </p>
            </div>
            <Award className="w-12 h-12 text-purple-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Photos This Month</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white">
                {students.reduce((acc, s) => acc + s.totalPhotos, 0)}
              </p>
            </div>
            <Camera className="w-12 h-12 text-orange-600" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                value={filters.searchTerm}
                onChange={(e) => setFilters({...filters, searchTerm: e.target.value})}
                placeholder="Search students..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Department</label>
            <select
              value={filters.department}
              onChange={(e) => setFilters({...filters, department: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Departments</option>
              <option value="Computer Science">Computer Science</option>
              <option value="Electronics">Electronics</option>
              <option value="Mechanical">Mechanical</option>
              <option value="Civil">Civil</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Health Status</label>
            <select
              value={filters.healthStatus}
              onChange={(e) => setFilters({...filters, healthStatus: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Status</option>
              <option value="excellent">Excellent</option>
              <option value="good">Good</option>
              <option value="fair">Fair</option>
              <option value="poor">Poor</option>
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={() => setFilters({ department: '', healthStatus: '', dateRange: '', searchTerm: '' })}
              className="w-full px-4 py-2 text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Students Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-900">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Student
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Tree Info
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Progress
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Health Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Care Score
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredStudents.map((student) => (
                <tr key={student.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {student.studentName}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {student.rollNumber} • {student.department}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {student.treeCode}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {student.treeSpecies}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm text-gray-900 dark:text-white">
                        {student.totalPhotos} photos
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {student.currentHeight}cm height
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getHealthStatusColor(student.healthStatus)}`}>
                      {getHealthStatusIcon(student.healthStatus)}
                      <span className="ml-1 capitalize">{student.healthStatus}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="text-sm font-medium text-gray-900 dark:text-white mr-2">
                        {student.careScore}
                      </div>
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-600 h-2 rounded-full" 
                          style={{ width: `${student.careScore}%` }}
                        />
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => {
                          setSelectedStudent(student);
                          setShowDetailModal(true);
                        }}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                        <MessageSquare className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Detail Modal */}
      {showDetailModal && selectedStudent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {selectedStudent.studentName} - Progress Details
              </h3>
              <button
                onClick={() => setShowDetailModal(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Roll Number</p>
                  <p className="text-gray-900 dark:text-white">{selectedStudent.rollNumber}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Department</p>
                  <p className="text-gray-900 dark:text-white">{selectedStudent.department}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Tree Code</p>
                  <p className="text-gray-900 dark:text-white">{selectedStudent.treeCode}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Species</p>
                  <p className="text-gray-900 dark:text-white">{selectedStudent.treeSpecies}</p>
                </div>
              </div>
              
              <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Progress Summary</h4>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{selectedStudent.totalPhotos}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Photos</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{selectedStudent.currentHeight}cm</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Height</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{selectedStudent.careScore}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Care Score</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StudentProgressMonitoring;
