import { Request, Response } from 'express';
export declare class InvitationController {
    getInvitations(req: Request, res: Response): Promise<void>;
    createInvitation(req: Request, res: Response): Promise<void>;
    updateInvitation(req: Request, res: Response): Promise<void>;
    deleteInvitation(req: Request, res: Response): Promise<void>;
    validateInvitationToken(req: Request, res: Response): Promise<void>;
    acceptInvitationPublic(req: Request, res: Response): Promise<void>;
    acceptInvitation(req: Request, res: Response): Promise<void>;
    rejectInvitation(req: Request, res: Response): Promise<void>;
    resendInvitation(req: Request, res: Response): Promise<void>;
}
export declare const invitationController: InvitationController;
//# sourceMappingURL=invitation.controller.d.ts.map