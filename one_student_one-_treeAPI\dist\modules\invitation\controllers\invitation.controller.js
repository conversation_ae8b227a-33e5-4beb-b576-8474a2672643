"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.invitationController = exports.InvitationController = void 0;
const database_1 = require("../../../config/database");
const toCamelCase = (str) => {
    return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
};
const convertKeysToCamelCase = (obj) => {
    if (obj === null || obj === undefined)
        return obj;
    if (Array.isArray(obj))
        return obj.map(convertKeysToCamelCase);
    if (typeof obj !== 'object')
        return obj;
    const converted = {};
    for (const [key, value] of Object.entries(obj)) {
        const camelKey = toCamelCase(key);
        converted[camelKey] = convertKeysToCamelCase(value);
    }
    return converted;
};
class InvitationController {
    async getInvitations(req, res) {
        try {
            const query = `
        SELECT
          i.*,
          u.name as sent_by_name,
          c.name as college_name,
          d.name as department_name
        FROM invitations i
        LEFT JOIN users u ON i.sent_by = u.id
        LEFT JOIN colleges c ON i.college_id = c.id
        LEFT JOIN departments d ON i.department_id = d.id
        ORDER BY i.created_at DESC
      `;
            const result = await database_1.pool.query(query);
            res.status(200).json({
                success: true,
                message: 'Invitations retrieved successfully',
                data: convertKeysToCamelCase(result.rows),
                pagination: {
                    page: 1,
                    limit: 50,
                    total: result.rows.length,
                    totalPages: 1,
                },
            });
        }
        catch (error) {
            console.error('Get invitations error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve invitations',
                error: error instanceof Error ? error.message : 'Unknown error',
            });
        }
    }
    async createInvitation(req, res) {
        try {
            const { email, role, college_id, department_id } = req.body;
            const sent_by = req.user.userId;
            const invitation_token = 'inv-' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
            const query = `
        INSERT INTO invitations (email, role, sent_by, college_id, department_id, status, invitation_token)
        VALUES ($1, $2, $3, $4, $5, 'pending', $6)
        RETURNING *
      `;
            const result = await database_1.pool.query(query, [email, role, sent_by, college_id, department_id, invitation_token]);
            const invitation = convertKeysToCamelCase(result.rows[0]);
            try {
                const { emailService } = require('../../../utils/email.service');
                const userQuery = `SELECT name FROM users WHERE id = $1`;
                const userResult = await database_1.pool.query(userQuery, [sent_by]);
                const senderName = userResult.rows[0]?.name || 'System Administrator';
                let emailOptions;
                const temporaryPassword = 'TempPassword@123';
                if (role === 'principal') {
                    const collegeQuery = `SELECT name FROM colleges WHERE id = $1`;
                    const collegeResult = await database_1.pool.query(collegeQuery, [college_id]);
                    const collegeName = collegeResult.rows[0]?.name || 'College';
                    emailOptions = emailService.generatePrincipalInvitationEmail(email.split('@')[0], email, collegeName, invitation_token, temporaryPassword);
                }
                else {
                    const deptQuery = `SELECT name FROM departments WHERE id = $1`;
                    const deptResult = await database_1.pool.query(deptQuery, [department_id]);
                    const departmentName = deptResult.rows[0]?.name || 'Department';
                    emailOptions = emailService.generateStaffHODInvitationEmail(email.split('@')[0], email, role, departmentName, senderName, invitation_token, temporaryPassword);
                }
                await emailService.sendEmail(emailOptions);
                console.log(`✅ Invitation email sent to: ${email}`);
            }
            catch (emailError) {
                console.error('⚠️  Failed to send invitation email:', emailError);
            }
            res.status(201).json({
                success: true,
                message: 'Invitation created successfully',
                data: invitation,
            });
        }
        catch (error) {
            console.error('Create invitation error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create invitation',
                error: error instanceof Error ? error.message : 'Unknown error',
            });
        }
    }
    async updateInvitation(req, res) {
        try {
            const { id } = req.params;
            const { email, role, collegeId, departmentId } = req.body;
            const query = `
        UPDATE invitations
        SET email = $1, role = $2, college_id = $3, department_id = $4
        WHERE id = $5
        RETURNING *
      `;
            const result = await database_1.pool.query(query, [email, role, collegeId, departmentId, id]);
            if (result.rows.length === 0) {
                res.status(404).json({
                    success: false,
                    message: 'Invitation not found',
                });
                return;
            }
            res.status(200).json({
                success: true,
                message: 'Invitation updated successfully',
                data: convertKeysToCamelCase(result.rows[0]),
            });
        }
        catch (error) {
            console.error('Update invitation error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update invitation',
            });
        }
    }
    async deleteInvitation(req, res) {
        try {
            const { id } = req.params;
            const query = 'DELETE FROM invitations WHERE id = $1';
            const result = await database_1.pool.query(query, [id]);
            if (result.rowCount === 0) {
                res.status(404).json({
                    success: false,
                    message: 'Invitation not found',
                });
                return;
            }
            res.status(200).json({
                success: true,
                message: 'Invitation deleted successfully',
            });
        }
        catch (error) {
            console.error('Delete invitation error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete invitation',
            });
        }
    }
    async validateInvitationToken(req, res) {
        try {
            const { token } = req.params;
            const query = `
        SELECT * FROM invitations
        WHERE invitation_token = $1 AND status = 'pending' AND expires_at > NOW()
      `;
            const result = await database_1.pool.query(query, [token]);
            if (result.rows.length === 0) {
                res.status(404).json({
                    success: false,
                    message: 'Invitation not found or has expired',
                });
                return;
            }
            const invitation = convertKeysToCamelCase(result.rows[0]);
            res.status(200).json({
                success: true,
                message: 'Invitation is valid',
                data: invitation,
            });
        }
        catch (error) {
            console.error('Validate invitation token error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to validate invitation token',
            });
        }
    }
    async acceptInvitationPublic(req, res) {
        try {
            const { invitationToken, name, phone, password } = req.body;
            const invitationQuery = `
        SELECT * FROM invitations
        WHERE invitation_token = $1 AND status = 'pending' AND expires_at > NOW()
      `;
            const invitationResult = await database_1.pool.query(invitationQuery, [invitationToken]);
            if (invitationResult.rows.length === 0) {
                res.status(404).json({
                    success: false,
                    message: 'Invitation not found or has expired',
                });
                return;
            }
            const invitation = invitationResult.rows[0];
            const userRepository = require('../../user/repositories/user.repository').userRepository;
            const existingUser = await userRepository.findByEmail(invitation.email);
            if (existingUser) {
                res.status(400).json({
                    success: false,
                    message: 'User with this email already exists',
                });
                return;
            }
            const { hashPassword } = require('../../../utils/auth.utils');
            const passwordHash = await hashPassword(password);
            const createUserData = {
                name,
                email: invitation.email,
                phone,
                role: invitation.role,
                passwordHash,
                status: 'active',
                collegeId: invitation.college_id,
                departmentId: invitation.department_id,
            };
            const newUser = await userRepository.createUser(createUserData);
            const acceptQuery = `
        UPDATE invitations
        SET status = 'accepted', accepted_at = NOW()
        WHERE id = $1
        RETURNING *
      `;
            await database_1.pool.query(acceptQuery, [invitation.id]);
            const { password_hash, ...userWithoutPassword } = newUser;
            res.status(201).json({
                success: true,
                message: 'Account created successfully! You can now log in.',
                data: {
                    user: userWithoutPassword,
                    invitation: convertKeysToCamelCase(invitation),
                },
            });
        }
        catch (error) {
            console.error('Accept invitation public error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to accept invitation and create account',
            });
        }
    }
    async acceptInvitation(req, res) {
        try {
            const { id } = req.params;
            const query = `
        UPDATE invitations
        SET status = 'accepted', accepted_at = NOW()
        WHERE id = $1
        RETURNING *
      `;
            const result = await database_1.pool.query(query, [id]);
            if (result.rows.length === 0) {
                res.status(404).json({
                    success: false,
                    message: 'Invitation not found',
                });
                return;
            }
            res.status(200).json({
                success: true,
                message: 'Invitation accepted successfully',
                data: convertKeysToCamelCase(result.rows[0]),
            });
        }
        catch (error) {
            console.error('Accept invitation error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to accept invitation',
            });
        }
    }
    async rejectInvitation(req, res) {
        try {
            const { id } = req.params;
            const query = `
        UPDATE invitations
        SET status = 'rejected', rejected_at = NOW()
        WHERE id = $1
        RETURNING *
      `;
            const result = await database_1.pool.query(query, [id]);
            if (result.rows.length === 0) {
                res.status(404).json({
                    success: false,
                    message: 'Invitation not found',
                });
                return;
            }
            res.status(200).json({
                success: true,
                message: 'Invitation rejected successfully',
                data: convertKeysToCamelCase(result.rows[0]),
            });
        }
        catch (error) {
            console.error('Reject invitation error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to reject invitation',
            });
        }
    }
    async resendInvitation(req, res) {
        try {
            const { id } = req.params;
            const invitationQuery = `
        SELECT i.*, u.name as sender_name, c.name as college_name, d.name as department_name
        FROM invitations i
        LEFT JOIN users u ON i.sent_by = u.id
        LEFT JOIN colleges c ON i.college_id = c.id
        LEFT JOIN departments d ON i.department_id = d.id
        WHERE i.id = $1 AND i.status = 'pending'
      `;
            const invitationResult = await database_1.pool.query(invitationQuery, [id]);
            if (invitationResult.rows.length === 0) {
                res.status(404).json({
                    success: false,
                    message: 'Invitation not found or already processed',
                });
                return;
            }
            const invitation = invitationResult.rows[0];
            const newInvitationToken = 'inv-' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
            const newExpiresAt = new Date();
            if (invitation.role === 'student') {
                newExpiresAt.setDate(newExpiresAt.getDate() + 14);
            }
            else {
                newExpiresAt.setDate(newExpiresAt.getDate() + 7);
            }
            const updateQuery = `
        UPDATE invitations
        SET invitation_token = $1, expires_at = $2
        WHERE id = $3
        RETURNING *
      `;
            const updateResult = await database_1.pool.query(updateQuery, [newInvitationToken, newExpiresAt, id]);
            const updatedInvitation = convertKeysToCamelCase(updateResult.rows[0]);
            try {
                const { emailService } = require('../../../utils/email.service');
                const temporaryPassword = 'TempPassword@123';
                let emailOptions;
                if (invitation.role === 'principal') {
                    emailOptions = emailService.generatePrincipalInvitationEmail(invitation.email.split('@')[0], invitation.email, invitation.college_name || 'College', newInvitationToken, temporaryPassword);
                }
                else if (invitation.role === 'student') {
                    emailOptions = emailService.generateStudentInvitationEmail(invitation.email.split('@')[0], invitation.email, 'N/A', 'N/A', 'N/A', invitation.department_name || 'Department', invitation.sender_name || 'Staff Member', newInvitationToken, temporaryPassword);
                }
                else {
                    emailOptions = emailService.generateStaffHODInvitationEmail(invitation.email.split('@')[0], invitation.email, invitation.role, invitation.department_name || 'Department', invitation.sender_name || 'Principal', newInvitationToken, temporaryPassword);
                }
                const emailSent = await emailService.sendEmail(emailOptions);
                if (emailSent) {
                    console.log(`✅ Invitation resent successfully to ${invitation.email}`);
                }
                else {
                    console.log(`⚠️ Invitation updated but email failed to send to ${invitation.email}`);
                }
            }
            catch (emailError) {
                console.error('⚠️ Failed to send resend invitation email:', emailError);
            }
            res.status(200).json({
                success: true,
                message: 'Invitation resent successfully',
                data: updatedInvitation,
            });
        }
        catch (error) {
            console.error('Resend invitation error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to resend invitation',
                error: error instanceof Error ? error.message : 'Unknown error',
            });
        }
    }
}
exports.InvitationController = InvitationController;
exports.invitationController = new InvitationController();
//# sourceMappingURL=invitation.controller.js.map