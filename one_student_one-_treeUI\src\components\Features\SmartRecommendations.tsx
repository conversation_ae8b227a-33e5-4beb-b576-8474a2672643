import React, { useState, useEffect } from 'react';
import { 
  <PERSON>bulb, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Calendar, 
  Droplets, 
  Sun, 
  Thermometer,
  Leaf,
  Camera,
  MessageSquare,
  Star,
  Target,
  Clock
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { ApiService } from '../../services/api';

interface Recommendation {
  id: string;
  type: 'care' | 'health' | 'growth' | 'documentation' | 'seasonal';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  actionItems: string[];
  dueDate?: string;
  completed: boolean;
  category: string;
  icon: React.ElementType;
  color: string;
}

interface WeatherData {
  temperature: number;
  humidity: number;
  rainfall: number;
  forecast: string;
}

const SmartRecommendations: React.FC = () => {
  const { user } = useAuth();
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  const [completedCount, setCompletedCount] = useState(0);

  useEffect(() => {
    loadRecommendations();
    loadWeatherData();
  }, []);

  const loadRecommendations = async () => {
    try {
      setLoading(true);
      
      // Get user's tree data and recent activity
      const [treeData, photos, userActivity] = await Promise.all([
        ApiService.getMyTree().catch(() => null),
        ApiService.getTreeImages().catch(() => []),
        ApiService.getUserActivity().catch(() => [])
      ]);

      // Generate smart recommendations based on data
      const smartRecommendations = generateRecommendations(treeData, photos, userActivity);
      setRecommendations(smartRecommendations);
      setCompletedCount(smartRecommendations.filter(r => r.completed).length);
      
    } catch (error) {
      console.error('Failed to load recommendations:', error);
      // Use mock data for demonstration
      setRecommendations(generateMockRecommendations());
    } finally {
      setLoading(false);
    }
  };

  const loadWeatherData = async () => {
    try {
      // In a real app, this would call a weather API
      setWeatherData({
        temperature: 28,
        humidity: 65,
        rainfall: 2.5,
        forecast: 'Partly cloudy with chance of rain'
      });
    } catch (error) {
      console.error('Failed to load weather data:', error);
    }
  };

  const generateRecommendations = (treeData: any, photos: any[], activity: any[]): Recommendation[] => {
    const recommendations: Recommendation[] = [];
    const now = new Date();
    const daysSinceLastPhoto = photos.length > 0 ? 
      Math.floor((now.getTime() - new Date(photos[0].createdAt).getTime()) / (1000 * 60 * 60 * 24)) : 30;

    // Photo documentation recommendations
    if (daysSinceLastPhoto > 7) {
      recommendations.push({
        id: 'photo-reminder',
        type: 'documentation',
        priority: daysSinceLastPhoto > 14 ? 'high' : 'medium',
        title: 'Update Tree Progress Photos',
        description: `It's been ${daysSinceLastPhoto} days since your last photo. Regular documentation helps track growth.`,
        actionItems: [
          'Take a full tree photo from the same angle',
          'Capture close-up shots of leaves and branches',
          'Measure and record current height',
          'Add notes about any changes observed'
        ],
        dueDate: new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString(),
        completed: false,
        category: 'Documentation',
        icon: Camera,
        color: 'blue'
      });
    }

    // Seasonal care recommendations
    const month = now.getMonth();
    if (month >= 2 && month <= 4) { // Spring
      recommendations.push({
        id: 'spring-care',
        type: 'seasonal',
        priority: 'medium',
        title: 'Spring Growth Season Care',
        description: 'Spring is the prime growing season. Your tree needs extra attention now.',
        actionItems: [
          'Increase watering frequency to 3-4 times per week',
          'Apply organic fertilizer around the base',
          'Prune any dead or damaged branches',
          'Check for new pest activity'
        ],
        completed: false,
        category: 'Seasonal Care',
        icon: Leaf,
        color: 'green'
      });
    }

    // Health monitoring recommendations
    if (treeData && photos.length > 1) {
      const latestPhoto = photos[0];
      const previousPhoto = photos[1];
      
      if (latestPhoto.measurements && previousPhoto.measurements) {
        const growthRate = ((latestPhoto.measurements.height - previousPhoto.measurements.height) / previousPhoto.measurements.height) * 100;
        
        if (growthRate < 5) {
          recommendations.push({
            id: 'growth-concern',
            type: 'health',
            priority: 'high',
            title: 'Slow Growth Detected',
            description: 'Your tree\'s growth rate has slowed. This might indicate stress or nutrient deficiency.',
            actionItems: [
              'Check soil moisture levels daily',
              'Examine leaves for discoloration or spots',
              'Ensure adequate sunlight (6-8 hours daily)',
              'Consider soil testing for nutrient levels'
            ],
            completed: false,
            category: 'Health Monitoring',
            icon: AlertTriangle,
            color: 'red'
          });
        }
      }
    }

    // Weather-based recommendations
    if (weatherData) {
      if (weatherData.temperature > 35) {
        recommendations.push({
          id: 'heat-protection',
          type: 'care',
          priority: 'high',
          title: 'Heat Wave Protection',
          description: 'High temperatures can stress your tree. Take protective measures.',
          actionItems: [
            'Water early morning and late evening',
            'Provide temporary shade during peak hours',
            'Mulch around the base to retain moisture',
            'Monitor for heat stress symptoms'
          ],
          completed: false,
          category: 'Weather Response',
          icon: Sun,
          color: 'orange'
        });
      }

      if (weatherData.rainfall < 1) {
        recommendations.push({
          id: 'drought-care',
          type: 'care',
          priority: 'medium',
          title: 'Drought Conditions Care',
          description: 'Low rainfall requires adjusted watering schedule.',
          actionItems: [
            'Increase watering frequency',
            'Water deeply rather than frequently',
            'Add mulch to conserve soil moisture',
            'Check soil moisture before watering'
          ],
          completed: false,
          category: 'Weather Response',
          icon: Droplets,
          color: 'blue'
        });
      }
    }

    return recommendations;
  };

  const generateMockRecommendations = (): Recommendation[] => [
    {
      id: '1',
      type: 'documentation',
      priority: 'high',
      title: 'Monthly Progress Photo Due',
      description: 'It\'s time for your monthly tree progress documentation. This helps track growth patterns.',
      actionItems: [
        'Take a full tree photo from the same angle as last month',
        'Capture close-up shots of leaves and new growth',
        'Measure and record current height and diameter',
        'Note any changes in leaf color or branch structure'
      ],
      dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      completed: false,
      category: 'Documentation',
      icon: Camera,
      color: 'blue'
    },
    {
      id: '2',
      type: 'care',
      priority: 'medium',
      title: 'Adjust Watering Schedule',
      description: 'Based on recent weather patterns, your tree needs adjusted watering.',
      actionItems: [
        'Water every 2-3 days instead of daily',
        'Water early morning or late evening',
        'Check soil moisture before watering',
        'Ensure proper drainage around the base'
      ],
      completed: false,
      category: 'Care Instructions',
      icon: Droplets,
      color: 'blue'
    },
    {
      id: '3',
      type: 'health',
      priority: 'low',
      title: 'Seasonal Fertilization',
      description: 'Spring is the ideal time to provide nutrients for healthy growth.',
      actionItems: [
        'Apply organic compost around the tree base',
        'Use balanced NPK fertilizer (10-10-10)',
        'Water thoroughly after fertilizing',
        'Monitor for signs of over-fertilization'
      ],
      completed: true,
      category: 'Health & Nutrition',
      icon: Leaf,
      color: 'green'
    }
  ];

  const handleCompleteRecommendation = async (id: string) => {
    try {
      setRecommendations(prev => 
        prev.map(rec => 
          rec.id === id ? { ...rec, completed: !rec.completed } : rec
        )
      );
      
      const updatedRec = recommendations.find(r => r.id === id);
      if (updatedRec) {
        setCompletedCount(prev => updatedRec.completed ? prev - 1 : prev + 1);
        // In a real app, this would sync with the backend
        // await ApiService.updateRecommendationStatus(id, !updatedRec.completed);
      }
    } catch (error) {
      console.error('Failed to update recommendation:', error);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-200 bg-red-50 dark:bg-red-900/20';
      case 'medium': return 'border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20';
      case 'low': return 'border-green-200 bg-green-50 dark:bg-green-900/20';
      default: return 'border-gray-200 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const filteredRecommendations = recommendations.filter(rec => 
    filter === 'all' || rec.priority === filter
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center">
            <Lightbulb className="w-8 h-8 mr-3 text-yellow-500" />
            Smart Recommendations
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Personalized care suggestions based on your tree's progress and environmental conditions
          </p>
        </div>
        
        <div className="mt-4 lg:mt-0 flex items-center space-x-3">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">{completedCount}</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">Completed</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">{recommendations.length - completedCount}</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">Pending</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Weather Alert */}
      {weatherData && (
        <div className="bg-gradient-to-r from-blue-50 to-sky-50 dark:from-blue-900/20 dark:to-sky-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Sun className="w-6 h-6 text-yellow-500 mr-3" />
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">Current Weather Conditions</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">{weatherData.forecast}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4 text-sm">
              <div className="text-center">
                <p className="font-medium text-gray-900 dark:text-white">{weatherData.temperature}°C</p>
                <p className="text-gray-600 dark:text-gray-400">Temp</p>
              </div>
              <div className="text-center">
                <p className="font-medium text-gray-900 dark:text-white">{weatherData.humidity}%</p>
                <p className="text-gray-600 dark:text-gray-400">Humidity</p>
              </div>
              <div className="text-center">
                <p className="font-medium text-gray-900 dark:text-white">{weatherData.rainfall}mm</p>
                <p className="text-gray-600 dark:text-gray-400">Rain</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
        {['all', 'high', 'medium', 'low'].map(priority => (
          <button
            key={priority}
            onClick={() => setFilter(priority as any)}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              filter === priority
                ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            {priority === 'all' ? 'All' : `${priority.charAt(0).toUpperCase() + priority.slice(1)} Priority`}
          </button>
        ))}
      </div>

      {/* Recommendations List */}
      <div className="space-y-4">
        {filteredRecommendations.map((recommendation) => {
          const Icon = recommendation.icon;
          return (
            <div
              key={recommendation.id}
              className={`border rounded-lg p-6 transition-all duration-200 ${
                recommendation.completed 
                  ? 'border-gray-200 bg-gray-50 dark:bg-gray-900/20 opacity-75' 
                  : getPriorityColor(recommendation.priority)
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center bg-${recommendation.color}-100 dark:bg-${recommendation.color}-900`}>
                    <Icon className={`w-6 h-6 text-${recommendation.color}-600 dark:text-${recommendation.color}-400`} />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className={`text-lg font-semibold ${recommendation.completed ? 'line-through text-gray-500' : 'text-gray-900 dark:text-white'}`}>
                        {recommendation.title}
                      </h3>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityBadgeColor(recommendation.priority)}`}>
                        {recommendation.priority.toUpperCase()}
                      </span>
                      <span className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full">
                        {recommendation.category}
                      </span>
                    </div>
                    
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      {recommendation.description}
                    </p>
                    
                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900 dark:text-white">Action Items:</h4>
                      <ul className="space-y-1">
                        {recommendation.actionItems.map((item, index) => (
                          <li key={index} className="flex items-start text-sm text-gray-600 dark:text-gray-400">
                            <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    {recommendation.dueDate && (
                      <div className="mt-4 flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <Clock className="w-4 h-4 mr-2" />
                        Due: {new Date(recommendation.dueDate).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                </div>
                
                <button
                  onClick={() => handleCompleteRecommendation(recommendation.id)}
                  className={`ml-4 p-2 rounded-lg transition-colors ${
                    recommendation.completed
                      ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400'
                      : 'bg-gray-100 text-gray-600 hover:bg-green-100 hover:text-green-600 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-green-900 dark:hover:text-green-400'
                  }`}
                >
                  <CheckCircle className="w-5 h-5" />
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {filteredRecommendations.length === 0 && (
        <div className="text-center py-12">
          <Star className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">All Caught Up!</h3>
          <p className="text-gray-600 dark:text-gray-400">
            {filter === 'all' 
              ? 'No recommendations at the moment. Keep up the great work!'
              : `No ${filter} priority recommendations right now.`
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default SmartRecommendations;
