"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const content_controller_1 = require("../controllers/content.controller");
const auth_middleware_1 = require("../../../middleware/auth.middleware");
const router = (0, express_1.Router)();
router.get('/guidelines', content_controller_1.contentController.getGuidelines);
router.get('/resources', content_controller_1.contentController.getResources);
router.post('/guidelines', auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)('admin'), content_controller_1.contentController.createGuideline);
router.put('/guidelines/:id', auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)('admin'), content_controller_1.contentController.updateGuideline);
router.delete('/guidelines/:id', auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)('admin'), content_controller_1.contentController.deleteGuideline);
router.post('/resources', auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)('admin'), content_controller_1.contentController.createResource);
router.put('/resources/:id', auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)('admin'), content_controller_1.contentController.updateResource);
router.delete('/resources/:id', auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)('admin'), content_controller_1.contentController.deleteResource);
exports.default = router;
//# sourceMappingURL=content.routes.js.map