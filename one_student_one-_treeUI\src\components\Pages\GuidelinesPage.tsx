import React, { useState, useEffect } from 'react';
import { TreePine, Camera, Calendar, MapPin, Droplets, Sun, AlertTriangle } from 'lucide-react';
import { ApiService } from '../../services/api';

interface Guideline {
  id: string;
  title: string;
  description: string;
  icon: string;
  tips: string[];
  displayOrder: number;
  isActive: boolean;
}

const GuidelinesPage: React.FC = () => {
  const [guidelines, setGuidelines] = useState<Guideline[]>([]);
  const [loading, setLoading] = useState(true);

  // Icon mapping
  const iconMap: Record<string, any> = {
    TreePine,
    Camera,
    Calendar,
    MapPin,
    Droplets,
    Sun,
    AlertTriangle
  };

  useEffect(() => {
    loadGuidelines();
  }, []);

  const loadGuidelines = async () => {
    try {
      setLoading(true);
      const data = await ApiService.getGuidelines();
      setGuidelines(data);
    } catch (error) {
      console.error('Failed to load guidelines:', error);
      // Fallback to default guidelines
      setGuidelines([
        {
          id: '1',
          icon: 'TreePine',
          title: 'Tree Selection',
          description: 'Choose a healthy sapling from the approved list. Ensure it\'s suitable for your local climate.',
          tips: [
            'Select native species when possible',
            'Check for healthy root system',
            'Avoid damaged or diseased saplings'
          ],
          displayOrder: 1,
          isActive: true
        },
        {
          id: '2',
          icon: 'MapPin',
          title: 'Location Selection',
          description: 'Pick a suitable location with adequate space for growth and proper sunlight.',
          tips: [
            'Ensure 6-8 hours of sunlight daily',
            'Check for underground utilities',
            'Allow 10-15 feet spacing from buildings'
          ],
          displayOrder: 2,
          isActive: true
        },
        {
          id: '3',
          icon: 'Droplets',
          title: 'Watering Schedule',
          description: 'Maintain consistent watering schedule, especially during the first year.',
          tips: [
            'Water deeply 2-3 times per week',
            'Increase frequency during hot weather',
            'Reduce watering during monsoon season'
          ],
          displayOrder: 3,
          isActive: true
        },
        {
          id: '4',
          icon: 'Camera',
          title: 'Progress Documentation',
          description: 'Take regular photos to track your tree\'s growth and health.',
          tips: [
            'Upload photos monthly',
            'Include height measurements',
            'Note any changes or concerns'
          ],
          displayOrder: 4,
          isActive: true
        },
        {
          id: '5',
          icon: 'Sun',
          title: 'Seasonal Care',
          description: 'Adjust care routine based on seasonal requirements.',
          tips: [
            'Mulch around base in summer',
            'Protect from strong winds',
            'Prune dead branches regularly'
          ],
          displayOrder: 5,
          isActive: true
        },
        {
          id: '6',
          icon: 'AlertTriangle',
          title: 'Problem Identification',
          description: 'Learn to identify common issues and when to seek help.',
          tips: [
            'Watch for pest infestations',
            'Monitor leaf color changes',
            'Report serious issues immediately'
          ],
          displayOrder: 6,
          isActive: true
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Tree Care Guidelines</h1>
        <p className="text-gray-600 dark:text-gray-400">Essential guidelines for successful tree planting and maintenance</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {guidelines.filter(g => g.isActive).map((guideline) => {
          const Icon = iconMap[guideline.icon] || TreePine;
          return (
            <div key={guideline.id} className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                  <Icon className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{guideline.title}</h3>
              </div>

              <p className="text-gray-600 dark:text-gray-400 mb-4">{guideline.description}</p>

              <ul className="space-y-2">
                {guideline.tips.map((tip, tipIndex) => (
                  <li key={tipIndex} className="flex items-start space-x-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">{tip}</span>
                  </li>
                ))}
              </ul>
            </div>
          );
        })}
      </div>

      {/* Additional Information */}
      <div className="mt-8 bg-green-50 border border-green-200 rounded-xl p-6">
        <div className="flex items-start space-x-3">
          <TreePine className="w-6 h-6 text-green-600 mt-1" />
          <div>
            <h3 className="text-lg font-semibold text-green-900 mb-2">Remember</h3>
            <p className="text-green-800">
              Your tree is a living commitment. Regular care and monitoring will ensure its healthy growth
              and contribute to a greener environment. Don't hesitate to reach out to your department
              coordinator if you need assistance.
            </p>
          </div>
        </div>
      </div>

      {/* Emergency Contact */}
      <div className="mt-6 bg-orange-50 border border-orange-200 rounded-xl p-6">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="w-6 h-6 text-orange-600 mt-1" />
          <div>
            <h3 className="text-lg font-semibold text-orange-900 mb-2">Need Help?</h3>
            <p className="text-orange-800 mb-2">
              If your tree shows signs of disease, pest infestation, or severe damage, contact your
              department coordinator immediately.
            </p>
            <p className="text-sm text-orange-700">
              Emergency Contact: Department Coordinator | Email: <EMAIL>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GuidelinesPage;
