import React, { useEffect, useState } from 'react';
import { AuthContext, useAuthProvider } from './hooks/useAuth';
import Login from './components/Auth/Login';
import DashboardLayout from './components/Layout/DashboardLayout';
import InvitationAcceptance from './components/Auth/InvitationAcceptance';
import { ToastContainer, useToast } from './components/UI/Toast';
import ErrorBoundary from './components/ErrorBoundary/ErrorBoundary';
import { ThemeProvider } from './contexts/ThemeContext';
import PerformanceMonitor from './components/Performance/PerformanceMonitor';

function App() {
  const auth = useAuthProvider();
  const toast = useToast();
  const [currentView, setCurrentView] = useState<'login' | 'dashboard' | 'invitation'>('login');
  const [invitationToken, setInvitationToken] = useState<string | null>(null);

  // Check URL for invitation token on mount
  useEffect(() => {
    const path = window.location.pathname;
    const invitationMatch = path.match(/\/invitation\/(.+)/);

    if (invitationMatch) {
      setInvitationToken(invitationMatch[1]);
      setCurrentView('invitation');
    } else if (auth.user) {
      setCurrentView('dashboard');
    } else {
      setCurrentView('login');
    }
  }, [auth.user]);

  // Handle navigation
  const navigateToLogin = () => {
    setCurrentView('login');
    setInvitationToken(null);
    window.history.pushState({}, '', '/login');
  };

  const navigateToDashboard = () => {
    setCurrentView('dashboard');
    window.history.pushState({}, '', '/dashboard');
  };

  if (auth.loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  const renderCurrentView = () => {
    if (currentView === 'invitation' && invitationToken) {
      return <InvitationAcceptance token={invitationToken} onNavigateToLogin={navigateToLogin} />;
    } else if (currentView === 'dashboard' && auth.user) {
      return <DashboardLayout />;
    } else {
      return <Login onLoginSuccess={navigateToDashboard} />;
    }
  };

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <AuthContext.Provider value={auth}>
          {renderCurrentView()}
          <ToastContainer toasts={toast.toasts} onClose={toast.removeToast} />
          <PerformanceMonitor
            enabled={import.meta.env.DEV}
            showWidget={true}
          />
        </AuthContext.Provider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;