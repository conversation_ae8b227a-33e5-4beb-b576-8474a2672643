"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const environment_1 = require("../config/environment");
const auth_routes_1 = __importDefault(require("../modules/auth/routes/auth.routes"));
const user_routes_1 = __importDefault(require("../modules/user/routes/user.routes"));
const college_routes_1 = __importDefault(require("../modules/college/routes/college.routes"));
const department_routes_1 = __importDefault(require("../modules/department/routes/department.routes"));
const tree_routes_1 = __importDefault(require("../modules/tree/routes/tree.routes"));
const dashboard_routes_1 = __importDefault(require("../modules/dashboard/routes/dashboard.routes"));
const invitation_routes_1 = __importDefault(require("../modules/invitation/routes/invitation.routes"));
const registration_routes_1 = __importDefault(require("../modules/registration/routes/registration.routes"));
const upload_routes_1 = __importDefault(require("../modules/upload/routes/upload.routes"));
const tree_selection_routes_1 = __importDefault(require("../modules/tree-selection/routes/tree-selection.routes"));
const approval_routes_1 = __importDefault(require("../modules/approval/routes/approval.routes"));
const tree_monitoring_routes_1 = __importDefault(require("../modules/tree-monitoring/routes/tree-monitoring.routes"));
const bulk_upload_routes_1 = __importDefault(require("../modules/bulk-upload/routes/bulk-upload.routes"));
const content_routes_1 = __importDefault(require("../modules/content/routes/content.routes"));
const router = (0, express_1.Router)();
const apiVersion = environment_1.config.server.apiVersion;
router.use(`/api/${apiVersion}/auth`, auth_routes_1.default);
router.use(`/api/${apiVersion}/users`, user_routes_1.default);
router.use(`/api/${apiVersion}/colleges`, college_routes_1.default);
router.use(`/api/${apiVersion}/departments`, department_routes_1.default);
router.use(`/api/${apiVersion}/trees`, tree_routes_1.default);
router.use(`/api/${apiVersion}/dashboard`, dashboard_routes_1.default);
router.use(`/api/${apiVersion}/invitations`, invitation_routes_1.default);
router.use(`/api/${apiVersion}/registration-requests`, registration_routes_1.default);
router.use(`/api/${apiVersion}/uploads`, upload_routes_1.default);
router.use(`/api/${apiVersion}/tree-selection`, tree_selection_routes_1.default);
router.use(`/api/${apiVersion}/approvals`, approval_routes_1.default);
router.use(`/api/${apiVersion}/tree-monitoring`, tree_monitoring_routes_1.default);
router.use(`/api/${apiVersion}/bulk-upload`, bulk_upload_routes_1.default);
router.use(`/api/${apiVersion}/content`, content_routes_1.default);
router.get(`/api/${apiVersion}`, (req, res) => {
    res.status(200).json({
        success: true,
        message: 'One Student One Tree API',
        version: apiVersion,
        endpoints: {
            authentication: `/api/${apiVersion}/auth`,
            users: `/api/${apiVersion}/users`,
            colleges: `/api/${apiVersion}/colleges`,
            departments: `/api/${apiVersion}/departments`,
            trees: `/api/${apiVersion}/trees`,
            dashboard: `/api/${apiVersion}/dashboard`,
        },
        documentation: environment_1.config.swagger.enabled ? `/api/${apiVersion}/docs` : 'Not available',
        health: '/health',
    });
});
exports.default = router;
//# sourceMappingURL=index.js.map