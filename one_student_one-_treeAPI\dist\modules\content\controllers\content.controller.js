"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.contentController = exports.ContentController = void 0;
const database_1 = require("../../../config/database");
const data_utils_1 = require("../../../utils/data.utils");
class ContentController {
    async getGuidelines(req, res) {
        try {
            const query = `
        SELECT * FROM guidelines 
        WHERE is_active = true 
        ORDER BY display_order ASC, created_at ASC
      `;
            const result = await database_1.pool.query(query);
            res.status(200).json({
                success: true,
                message: 'Guidelines retrieved successfully',
                data: result.rows.map(data_utils_1.convertKeysToCamelCase),
            });
        }
        catch (error) {
            console.error('Get guidelines error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve guidelines',
            });
        }
    }
    async getResources(req, res) {
        try {
            const query = `
        SELECT * FROM resources 
        WHERE is_active = true 
        ORDER BY category, display_order ASC, created_at ASC
      `;
            const result = await database_1.pool.query(query);
            res.status(200).json({
                success: true,
                message: 'Resources retrieved successfully',
                data: result.rows.map(data_utils_1.convertKeysToCamelCase),
            });
        }
        catch (error) {
            console.error('Get resources error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve resources',
            });
        }
    }
    async createGuideline(req, res) {
        try {
            const { title, description, icon, tips, displayOrder } = req.body;
            const query = `
        INSERT INTO guidelines (title, description, icon, tips, display_order, created_by)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *
      `;
            const result = await database_1.pool.query(query, [
                title,
                description,
                icon,
                JSON.stringify(tips),
                displayOrder || 0,
                req.user.id
            ]);
            res.status(201).json({
                success: true,
                message: 'Guideline created successfully',
                data: (0, data_utils_1.convertKeysToCamelCase)(result.rows[0]),
            });
        }
        catch (error) {
            console.error('Create guideline error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create guideline',
            });
        }
    }
    async updateGuideline(req, res) {
        try {
            const { id } = req.params;
            const { title, description, icon, tips, displayOrder, isActive } = req.body;
            const query = `
        UPDATE guidelines 
        SET title = $1, description = $2, icon = $3, tips = $4, 
            display_order = $5, is_active = $6, updated_at = NOW()
        WHERE id = $7
        RETURNING *
      `;
            const result = await database_1.pool.query(query, [
                title,
                description,
                icon,
                JSON.stringify(tips),
                displayOrder,
                isActive,
                id
            ]);
            if (result.rows.length === 0) {
                res.status(404).json({
                    success: false,
                    message: 'Guideline not found',
                });
                return;
            }
            res.status(200).json({
                success: true,
                message: 'Guideline updated successfully',
                data: (0, data_utils_1.convertKeysToCamelCase)(result.rows[0]),
            });
        }
        catch (error) {
            console.error('Update guideline error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update guideline',
            });
        }
    }
    async deleteGuideline(req, res) {
        try {
            const { id } = req.params;
            const query = `
        UPDATE guidelines 
        SET is_active = false, updated_at = NOW()
        WHERE id = $1
        RETURNING *
      `;
            const result = await database_1.pool.query(query, [id]);
            if (result.rows.length === 0) {
                res.status(404).json({
                    success: false,
                    message: 'Guideline not found',
                });
                return;
            }
            res.status(200).json({
                success: true,
                message: 'Guideline deleted successfully',
            });
        }
        catch (error) {
            console.error('Delete guideline error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete guideline',
            });
        }
    }
    async createResource(req, res) {
        try {
            const { category, title, description, type, size, link, displayOrder } = req.body;
            const query = `
        INSERT INTO resources (category, title, description, type, size, link, display_order, created_by)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `;
            const result = await database_1.pool.query(query, [
                category,
                title,
                description,
                type,
                size,
                link,
                displayOrder || 0,
                req.user.id
            ]);
            res.status(201).json({
                success: true,
                message: 'Resource created successfully',
                data: (0, data_utils_1.convertKeysToCamelCase)(result.rows[0]),
            });
        }
        catch (error) {
            console.error('Create resource error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create resource',
            });
        }
    }
    async updateResource(req, res) {
        try {
            const { id } = req.params;
            const { category, title, description, type, size, link, displayOrder, isActive } = req.body;
            const query = `
        UPDATE resources 
        SET category = $1, title = $2, description = $3, type = $4, 
            size = $5, link = $6, display_order = $7, is_active = $8, updated_at = NOW()
        WHERE id = $9
        RETURNING *
      `;
            const result = await database_1.pool.query(query, [
                category,
                title,
                description,
                type,
                size,
                link,
                displayOrder,
                isActive,
                id
            ]);
            if (result.rows.length === 0) {
                res.status(404).json({
                    success: false,
                    message: 'Resource not found',
                });
                return;
            }
            res.status(200).json({
                success: true,
                message: 'Resource updated successfully',
                data: (0, data_utils_1.convertKeysToCamelCase)(result.rows[0]),
            });
        }
        catch (error) {
            console.error('Update resource error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update resource',
            });
        }
    }
    async deleteResource(req, res) {
        try {
            const { id } = req.params;
            const query = `
        UPDATE resources 
        SET is_active = false, updated_at = NOW()
        WHERE id = $1
        RETURNING *
      `;
            const result = await database_1.pool.query(query, [id]);
            if (result.rows.length === 0) {
                res.status(404).json({
                    success: false,
                    message: 'Resource not found',
                });
                return;
            }
            res.status(200).json({
                success: true,
                message: 'Resource deleted successfully',
            });
        }
        catch (error) {
            console.error('Delete resource error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete resource',
            });
        }
    }
}
exports.ContentController = ContentController;
exports.contentController = new ContentController();
//# sourceMappingURL=content.controller.js.map