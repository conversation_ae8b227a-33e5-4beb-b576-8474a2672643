const { pool } = require('./dist/config/database');

async function findTestUsers() {
  try {
    console.log('🔍 Looking for test users...');
    
    // Look for specific test users mentioned in memories
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];
    
    for (const email of testEmails) {
      const result = await pool.query('SELECT email, name, role FROM users WHERE email = $1', [email]);
      if (result.rows.length > 0) {
        const user = result.rows[0];
        console.log(`✅ Found: ${user.name} (${user.email}) - ${user.role}`);
      } else {
        console.log(`❌ Not found: ${email}`);
      }
    }
    
    // Look for any users with 'test' in email
    const testResult = await pool.query("SELECT email, name, role FROM users WHERE email LIKE '%test%' OR email LIKE '%demo%'");
    if (testResult.rows.length > 0) {
      console.log('\n📋 Test/Demo users found:');
      testResult.rows.forEach(user => {
        console.log(`- ${user.name} (${user.email}) - ${user.role}`);
      });
    }
    
    // Look for students with simple emails
    const studentResult = await pool.query("SELECT email, name, role FROM users WHERE role = 'student' AND (email LIKE '%@gvec.edu%' OR email LIKE '%student%') LIMIT 10");
    if (studentResult.rows.length > 0) {
      console.log('\n👨‍🎓 Student accounts found:');
      studentResult.rows.forEach(user => {
        console.log(`- ${user.name} (${user.email})`);
      });
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    process.exit(0);
  }
}

findTestUsers();
