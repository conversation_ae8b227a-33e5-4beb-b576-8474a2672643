const { pool } = require('./dist/config/database');

async function testContentEndpoints() {
  try {
    console.log('Testing database connection...');
    
    // Test guidelines query
    console.log('\n1. Testing guidelines query...');
    const guidelinesQuery = `
      SELECT * FROM guidelines 
      WHERE is_active = true 
      ORDER BY display_order ASC, created_at ASC
    `;
    
    const guidelinesResult = await pool.query(guidelinesQuery);
    console.log(`Found ${guidelinesResult.rows.length} guidelines`);
    
    if (guidelinesResult.rows.length > 0) {
      console.log('Sample guideline:', {
        id: guidelinesResult.rows[0].id,
        title: guidelinesResult.rows[0].title,
        description: guidelinesResult.rows[0].description?.substring(0, 50) + '...'
      });
    }
    
    // Test resources query
    console.log('\n2. Testing resources query...');
    const resourcesQuery = `
      SELECT * FROM resources 
      WHERE is_active = true 
      ORDER BY category, display_order ASC, created_at ASC
    `;
    
    const resourcesResult = await pool.query(resourcesQuery);
    console.log(`Found ${resourcesResult.rows.length} resources`);
    
    if (resourcesResult.rows.length > 0) {
      console.log('Sample resource:', {
        id: resourcesResult.rows[0].id,
        title: resourcesResult.rows[0].title,
        category: resourcesResult.rows[0].category
      });
    }
    
    // Test API endpoints
    console.log('\n3. Testing API endpoints...');
    const axios = require('axios');
    
    try {
      const guidelinesResponse = await axios.get('http://localhost:3000/api/v1/content/guidelines');
      console.log('Guidelines API success:', guidelinesResponse.data.success);
      console.log('Guidelines count from API:', guidelinesResponse.data.data?.length || 0);
    } catch (error) {
      console.log('Guidelines API error:', error.response?.status, error.response?.data || error.message);
      
      // Check if it's a database connection issue
      if (error.response?.data?.message) {
        console.log('Error message:', error.response.data.message);
      }
    }
    
    try {
      const resourcesResponse = await axios.get('http://localhost:3000/api/v1/content/resources');
      console.log('Resources API success:', resourcesResponse.data.success);
      console.log('Resources count from API:', resourcesResponse.data.data?.length || 0);
    } catch (error) {
      console.log('Resources API error:', error.response?.status, error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
  } finally {
    process.exit(0);
  }
}

testContentEndpoints();
