<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Viewport and Mobile Optimization -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#22c55e" />
    <meta name="background-color" content="#ffffff" />
    <meta name="display" content="standalone" />
    <meta name="orientation" content="portrait-primary" />

    <!-- Apple PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="TreeCare" />
    <meta name="apple-touch-fullscreen" content="yes" />

    <!-- Microsoft PWA Meta Tags -->
    <meta name="msapplication-TileColor" content="#22c55e" />
    <meta name="msapplication-config" content="/browserconfig.xml" />

    <!-- SEO and Description -->
    <meta name="description" content="Tree monitoring and care application for educational institutions. Track your tree's growth and contribute to environmental sustainability." />
    <meta name="keywords" content="tree care, environment, education, sustainability, monitoring" />
    <meta name="author" content="One Student One Tree Initiative" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="One Student One Tree" />
    <meta property="og:description" content="Tree monitoring and care application for educational institutions" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/icons/icon-512x512.png" />
    <meta property="og:url" content="/" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="One Student One Tree" />
    <meta name="twitter:description" content="Tree monitoring and care application for educational institutions" />
    <meta name="twitter:image" content="/icons/icon-512x512.png" />

    <!-- Preload Critical Resources -->
    <link rel="preload" href="data:application/octet-stream;base64,aW1wb3J0IHsgU3RyaWN0TW9kZSB9IGZyb20gJ3JlYWN0JzsNCmltcG9ydCB7IGNyZWF0ZVJvb3QgfSBmcm9tICdyZWFjdC1kb20vY2xpZW50JzsNCmltcG9ydCBBcHAgZnJvbSAnLi9BcHAudHN4JzsNCmltcG9ydCAnLi9pbmRleC5jc3MnOw0KDQpjcmVhdGVSb290KGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdyb290JykhKS5yZW5kZXIoDQogIDxTdHJpY3RNb2RlPg0KICAgIDxBcHAgLz4NCiAgPC9TdHJpY3RNb2RlPg0KKTsNCg==" as="script" crossorigin="anonymous" />

    <title>One Student One Tree - Tree Care & Monitoring</title>
    <script type="module" crossorigin src="/assets/index-7ZBNdQdD.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-C7UpdzzG.css">
  </head>
  <body>
    <div id="root"></div>

    <!-- Loading Screen -->
    <div id="loading-screen" style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #22c55e, #3b82f6);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      color: white;
      font-family: system-ui, -apple-system, sans-serif;
    ">
      <div style="
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255,255,255,0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      "></div>
      <h2 style="margin: 0; font-size: 24px; font-weight: bold;">One Student One Tree</h2>
      <p style="margin: 10px 0 0 0; opacity: 0.8;">Loading your tree care dashboard...</p>
    </div>

    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>



    <!-- Service Worker Registration -->
    <script>
      // Remove loading screen when app loads
      window.addEventListener('load', () => {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
          setTimeout(() => {
            loadingScreen.style.opacity = '0';
            loadingScreen.style.transition = 'opacity 0.5s ease-out';
            setTimeout(() => {
              loadingScreen.remove();
            }, 500);
          }, 1000);
        }
      });

      // Service Worker completely removed for development

      // PWA Install Prompt
      let deferredPrompt;
      window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;

        // Show install button or banner
        const installBanner = document.createElement('div');
        installBanner.innerHTML = `
          <div style="
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: #22c55e;
            color: white;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            display: flex;
            align-items: center;
            justify-content: space-between;
            z-index: 1000;
            font-family: system-ui, -apple-system, sans-serif;
          ">
            <div>
              <div style="font-weight: bold; margin-bottom: 4px;">Install TreeCare App</div>
              <div style="font-size: 14px; opacity: 0.9;">Get the full app experience</div>
            </div>
            <div>
              <button onclick="installApp()" style="
                background: white;
                color: #22c55e;
                border: none;
                padding: 8px 16px;
                border-radius: 8px;
                font-weight: bold;
                margin-right: 8px;
                cursor: pointer;
              ">Install</button>
              <button onclick="this.parentElement.parentElement.parentElement.remove()" style="
                background: transparent;
                color: white;
                border: 1px solid rgba(255,255,255,0.3);
                padding: 8px 12px;
                border-radius: 8px;
                cursor: pointer;
              ">×</button>
            </div>
          </div>
        `;
        document.body.appendChild(installBanner);
      });

      function installApp() {
        if (deferredPrompt) {
          deferredPrompt.prompt();
          deferredPrompt.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
              console.log('User accepted the install prompt');
            }
            deferredPrompt = null;
          });
        }
      }
    </script>
  </body>
</html>
