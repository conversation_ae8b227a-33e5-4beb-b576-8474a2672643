"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.treeFilterSchema = exports.userFilterSchema = exports.paginationSchema = exports.updateTreeSchema = exports.createTreeSchema = exports.updateDepartmentSchema = exports.createDepartmentSchema = exports.updateCollegeSchema = exports.createCollegeSchema = exports.updateProfileSchema = exports.updateUserSchema = exports.createUserSchema = exports.resetPasswordSchema = exports.changePasswordSchema = exports.refreshTokenSchema = exports.loginSchema = exports.userRoleSchema = exports.uuidSchema = exports.phoneSchema = exports.emailSchema = exports.passwordSchema = void 0;
const joi_1 = __importDefault(require("joi"));
const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const phonePattern = /^\+?[\d\s\-\(\)]+$/;
const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
exports.passwordSchema = joi_1.default.string()
    .min(8)
    .max(128)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/)
    .required()
    .messages({
    'string.min': 'Password must be at least 8 characters long',
    'string.max': 'Password must not exceed 128 characters',
    'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
    'any.required': 'Password is required',
});
exports.emailSchema = joi_1.default.string()
    .email()
    .pattern(emailPattern)
    .lowercase()
    .required()
    .messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required',
});
exports.phoneSchema = joi_1.default.string()
    .pattern(phonePattern)
    .min(10)
    .max(20)
    .optional()
    .messages({
    'string.pattern.base': 'Please provide a valid phone number',
    'string.min': 'Phone number must be at least 10 characters',
    'string.max': 'Phone number must not exceed 20 characters',
});
exports.uuidSchema = joi_1.default.string()
    .pattern(uuidPattern)
    .required()
    .messages({
    'string.pattern.base': 'Please provide a valid UUID',
    'any.required': 'ID is required',
});
exports.userRoleSchema = joi_1.default.string()
    .valid('admin', 'principal', 'hod', 'staff', 'student')
    .required()
    .messages({
    'any.only': 'Role must be one of: admin, principal, hod, staff, student',
    'any.required': 'Role is required',
});
exports.loginSchema = joi_1.default.object({
    email: exports.emailSchema,
    password: joi_1.default.string().required().messages({
        'any.required': 'Password is required',
    }),
});
exports.refreshTokenSchema = joi_1.default.object({
    refreshToken: joi_1.default.string().required().messages({
        'any.required': 'Refresh token is required',
    }),
});
exports.changePasswordSchema = joi_1.default.object({
    currentPassword: joi_1.default.string().required().messages({
        'any.required': 'Current password is required',
    }),
    newPassword: exports.passwordSchema,
    confirmPassword: joi_1.default.string()
        .valid(joi_1.default.ref('newPassword'))
        .required()
        .messages({
        'any.only': 'Password confirmation does not match',
        'any.required': 'Password confirmation is required',
    }),
});
exports.resetPasswordSchema = joi_1.default.object({
    newPassword: exports.passwordSchema,
    confirmPassword: joi_1.default.string()
        .valid(joi_1.default.ref('newPassword'))
        .required()
        .messages({
        'any.only': 'Password confirmation does not match',
        'any.required': 'Password confirmation is required',
    }),
});
exports.createUserSchema = joi_1.default.object({
    email: exports.emailSchema,
    password: exports.passwordSchema,
    name: joi_1.default.string().min(2).max(100).required().messages({
        'string.min': 'Name must be at least 2 characters long',
        'string.max': 'Name must not exceed 100 characters',
        'any.required': 'Name is required',
    }),
    role: exports.userRoleSchema,
    phone: exports.phoneSchema,
    collegeId: joi_1.default.string().pattern(uuidPattern).optional(),
    departmentId: joi_1.default.string().pattern(uuidPattern).optional(),
    classInCharge: joi_1.default.string().max(50).optional(),
    class: joi_1.default.string().max(50).optional(),
    semester: joi_1.default.string().max(10).optional(),
    rollNumber: joi_1.default.string().max(50).optional(),
});
exports.updateUserSchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(100).optional(),
    phone: exports.phoneSchema,
    status: joi_1.default.string().valid('active', 'inactive', 'pending').optional(),
    collegeId: joi_1.default.string().pattern(uuidPattern).optional(),
    departmentId: joi_1.default.string().pattern(uuidPattern).optional(),
    classInCharge: joi_1.default.string().max(50).optional(),
    class: joi_1.default.string().max(50).optional(),
    semester: joi_1.default.string().max(10).optional(),
    rollNumber: joi_1.default.string().max(50).optional(),
    profileImageUrl: joi_1.default.string().uri().optional(),
});
exports.updateProfileSchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(100).optional(),
    phone: exports.phoneSchema,
    profileImageUrl: joi_1.default.string().uri().optional(),
});
exports.createCollegeSchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(255).required().messages({
        'string.min': 'College name must be at least 2 characters long',
        'string.max': 'College name must not exceed 255 characters',
        'any.required': 'College name is required',
    }),
    address: joi_1.default.string().min(10).max(500).required().messages({
        'string.min': 'Address must be at least 10 characters long',
        'string.max': 'Address must not exceed 500 characters',
        'any.required': 'Address is required',
    }),
    phone: exports.phoneSchema.required(),
    email: exports.emailSchema,
    website: joi_1.default.string().uri().optional(),
    established: joi_1.default.string().pattern(/^\d{4}$/).optional().messages({
        'string.pattern.base': 'Established year must be a 4-digit year',
    }),
    principalId: joi_1.default.string().pattern(uuidPattern).optional(),
    principalName: joi_1.default.string().min(2).max(100).optional().messages({
        'string.min': 'Principal name must be at least 2 characters long',
        'string.max': 'Principal name must not exceed 100 characters',
    }),
    principalEmail: exports.emailSchema.optional(),
    principalPhone: exports.phoneSchema.optional(),
});
exports.updateCollegeSchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(255).optional(),
    address: joi_1.default.string().min(10).max(500).optional(),
    phone: exports.phoneSchema,
    email: exports.emailSchema.optional(),
    website: joi_1.default.string().uri().optional(),
    established: joi_1.default.string().pattern(/^\d{4}$/).optional(),
    principalId: joi_1.default.string().pattern(uuidPattern).optional(),
    status: joi_1.default.string().valid('active', 'inactive', 'suspended').optional(),
});
exports.createDepartmentSchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(255).required().messages({
        'string.min': 'Department name must be at least 2 characters long',
        'string.max': 'Department name must not exceed 255 characters',
        'any.required': 'Department name is required',
    }),
    code: joi_1.default.string().min(2).max(10).uppercase().required().messages({
        'string.min': 'Department code must be at least 2 characters long',
        'string.max': 'Department code must not exceed 10 characters',
        'any.required': 'Department code is required',
    }),
    collegeId: joi_1.default.string().pattern(uuidPattern).required(),
    hodId: joi_1.default.string().pattern(uuidPattern).optional(),
    totalStudents: joi_1.default.number().integer().min(0).optional(),
    totalStaff: joi_1.default.number().integer().min(0).optional(),
    established: joi_1.default.string().pattern(/^\d{4}$/).optional(),
});
exports.updateDepartmentSchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(255).optional(),
    code: joi_1.default.string().min(2).max(10).uppercase().optional(),
    hodId: joi_1.default.string().pattern(uuidPattern).optional(),
    totalStudents: joi_1.default.number().integer().min(0).optional(),
    totalStaff: joi_1.default.number().integer().min(0).optional(),
    established: joi_1.default.string().pattern(/^\d{4}$/).optional(),
});
exports.createTreeSchema = joi_1.default.object({
    treeCode: joi_1.default.string().min(3).max(50).optional().messages({
        'string.min': 'Tree code must be at least 3 characters long',
        'string.max': 'Tree code must not exceed 50 characters',
    }),
    species: joi_1.default.string().min(2).max(255).required().messages({
        'string.min': 'Species must be at least 2 characters long',
        'string.max': 'Species must not exceed 255 characters',
        'any.required': 'Species is required',
    }),
    plantedDate: joi_1.default.date().max('now').optional().messages({
        'date.max': 'Planted date cannot be in the future',
    }),
    locationDescription: joi_1.default.string().max(500).optional(),
    latitude: joi_1.default.number().min(-90).max(90).optional(),
    longitude: joi_1.default.number().min(-180).max(180).optional(),
    assignedStudentId: joi_1.default.string().pattern(uuidPattern).optional(),
    assignedDate: joi_1.default.date().optional(),
    status: joi_1.default.string().valid('available', 'assigned', 'healthy', 'needs_attention', 'deceased', 'replaced').optional(),
    collegeId: joi_1.default.string().pattern(uuidPattern).required(),
    departmentId: joi_1.default.string().pattern(uuidPattern).optional(),
    notes: joi_1.default.string().max(1000).optional(),
});
exports.updateTreeSchema = joi_1.default.object({
    species: joi_1.default.string().min(2).max(255).optional(),
    locationDescription: joi_1.default.string().max(500).optional(),
    latitude: joi_1.default.number().min(-90).max(90).optional(),
    longitude: joi_1.default.number().min(-180).max(180).optional(),
    assignedStudentId: joi_1.default.string().pattern(uuidPattern).optional(),
    assignedDate: joi_1.default.date().optional(),
    status: joi_1.default.string().valid('assigned', 'healthy', 'needs_attention', 'deceased', 'replaced').optional(),
    departmentId: joi_1.default.string().pattern(uuidPattern).optional(),
    notes: joi_1.default.string().max(1000).optional(),
});
exports.paginationSchema = joi_1.default.object({
    page: joi_1.default.number().integer().min(1).default(1),
    limit: joi_1.default.number().integer().min(1).max(10000).default(1000),
    sortBy: joi_1.default.string().optional(),
    sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc'),
});
exports.userFilterSchema = exports.paginationSchema.keys({
    role: exports.userRoleSchema.optional(),
    status: joi_1.default.string().valid('active', 'inactive', 'pending').optional(),
    collegeId: joi_1.default.string().pattern(uuidPattern).optional(),
    departmentId: joi_1.default.string().pattern(uuidPattern).optional(),
    search: joi_1.default.string().max(100).optional(),
});
exports.treeFilterSchema = exports.paginationSchema.keys({
    status: joi_1.default.string().valid('available', 'assigned', 'healthy', 'needs_attention', 'deceased', 'replaced').optional(),
    collegeId: joi_1.default.string().pattern(uuidPattern).optional(),
    departmentId: joi_1.default.string().pattern(uuidPattern).optional(),
    assignedStudentId: joi_1.default.string().pattern(uuidPattern).optional(),
    species: joi_1.default.string().max(100).optional(),
});
//# sourceMappingURL=validation.schemas.js.map