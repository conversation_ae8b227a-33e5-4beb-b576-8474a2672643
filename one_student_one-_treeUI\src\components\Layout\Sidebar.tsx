import React from 'react';
import {
  Squares2X2Icon,
  UsersIcon,
  AcademicCapIcon,
  BuildingOfficeIcon,
  CheckCircleIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  PuzzlePieceIcon,
  DocumentTextIcon,
  BookOpenIcon,
  EnvelopeIcon,
  ClipboardDocumentCheckIcon,
  UserIcon,
  ArrowUpTrayIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../hooks/useAuth';
import ThemeToggle from '../UI/ThemeToggle';

interface SidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeTab, onTabChange, isOpen, onClose }) => {
  const { user, logout } = useAuth();

  const getMenuItems = () => {
    const baseItems = [
      { id: 'dashboard', label: 'Dashboard', icon: Squares2X2Icon }
    ];

    switch (user?.role) {
      case 'admin':
        return [
          ...baseItems,
          { id: 'data-visualization', label: 'Data Visualization', icon: ChartBarIcon },
          { id: 'bulk-upload', label: 'Bulk Upload Colleges', icon: ArrowUpTrayIcon },
          { id: 'colleges', label: 'Colleges', icon: BuildingOfficeIcon },
          { id: 'users', label: 'Users', icon: UsersIcon },
          { id: 'content-management', label: 'Content Management', icon: DocumentTextIcon },
          { id: 'reports', label: 'Reports', icon: DocumentTextIcon },
          { id: 'invitations', label: 'Invitations', icon: EnvelopeIcon },
          { id: 'requests', label: 'Requests', icon: CheckCircleIcon },
          { id: 'settings', label: 'Settings', icon: UserIcon }
        ];
      case 'principal':
        return [
          ...baseItems,
          { id: 'bulk-upload-staff', label: 'Bulk Upload Staff/HODs', icon: ArrowUpTrayIcon },
          { id: 'staff-management', label: 'Staff Management', icon: UsersIcon },
          { id: 'departments', label: 'Departments', icon: BuildingOfficeIcon },
          { id: 'students', label: 'Students', icon: AcademicCapIcon },
          { id: 'reports', label: 'Reports', icon: DocumentTextIcon },
          { id: 'settings', label: 'Settings', icon: UserIcon }
        ];
      case 'hod':
        return [
          ...baseItems,
          { id: 'bulk-upload-students', label: 'Bulk Upload Students', icon: ArrowUpTrayIcon },
          { id: 'department-summary', label: 'Department Summary', icon: BuildingOfficeIcon },
          { id: 'student-monitoring', label: 'Progress Monitoring', icon: ChartBarIcon },
          { id: 'department-staff', label: 'Department Staff', icon: UsersIcon },
          { id: 'department-students', label: 'Students', icon: AcademicCapIcon },
          { id: 'tree-management', label: 'Tree Management', icon: PuzzlePieceIcon },
          { id: 'student-requests', label: 'Student Requests', icon: CheckCircleIcon }
        ];
      case 'staff':
        return [
          ...baseItems,
          { id: 'bulk-upload-students', label: 'Bulk Upload Students', icon: ArrowUpTrayIcon },
          { id: 'my-students', label: 'My Students', icon: AcademicCapIcon },
          { id: 'student-monitoring', label: 'Progress Monitoring', icon: ChartBarIcon },
          { id: 'student-management', label: 'Student Management', icon: UsersIcon },
          { id: 'tree-management', label: 'Tree Management', icon: PuzzlePieceIcon },
          { id: 'student-requests', label: 'Student Requests', icon: CheckCircleIcon }
        ];
      case 'student':
        return [
          ...baseItems,
          { id: 'smart-recommendations', label: 'Smart Recommendations', icon: PuzzlePieceIcon },
          { id: 'tree-selection', label: 'Tree Selection', icon: PuzzlePieceIcon },
          { id: 'my-tree', label: 'My Tree Progress', icon: PuzzlePieceIcon },
          { id: 'guidelines', label: 'Guidelines', icon: DocumentTextIcon },
          { id: 'resources', label: 'Resources', icon: BookOpenIcon },
          { id: 'settings', label: 'Account', icon: UserIcon }
        ];
      default:
        return baseItems;
    }
  };

  const menuItems = getMenuItems();

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <div className={`
        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700
        min-h-screen flex flex-col transform transition-transform duration-300 ease-in-out safe-area-top safe-area-bottom
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
      {/* Logo Section */}
      <div className="p-4 lg:p-6 border-b border-gray-100 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
            <PuzzlePieceIcon className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-base lg:text-lg font-bold text-gray-900 dark:text-white">One Student</h1>
            <p className="text-xs lg:text-sm text-gray-500 dark:text-gray-400">One Tree</p>
          </div>
          {/* Mobile Close Button */}
          <button
            onClick={onClose}
            className="lg:hidden ml-auto p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 p-3 lg:p-4 overflow-y-auto">
        <div className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => {
                  onTabChange(item.id);
                  onClose(); // Close sidebar on mobile after selection
                }}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                  isActive
                    ? 'bg-green-600 text-white'
                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium text-sm lg:text-base">{item.label}</span>
              </button>
            );
          })}
        </div>
      </nav>

      {/* User Profile Section */}
      <div className="p-3 lg:p-4 border-t border-gray-100 dark:border-gray-700">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <span className="text-xs lg:text-sm font-medium text-gray-600 dark:text-gray-300">
              {user?.name?.charAt(0) || 'U'}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-xs lg:text-sm font-medium text-gray-900 dark:text-white truncate">
              {user?.name || 'User'}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
              {user?.role || 'User'}
            </p>
          </div>
          <ThemeToggle />
        </div>

        <div className="space-y-2">
          <button
            onClick={() => onTabChange('settings')}
            className={`w-full flex items-center space-x-3 px-4 py-2 rounded-lg text-left transition-colors ${
              activeTab === 'settings'
                ? 'bg-gray-100 text-gray-900'
                : 'text-gray-600 hover:bg-gray-50'
            }`}
          >
            <CogIcon className="w-4 h-4" />
            <span className="text-xs lg:text-sm">Settings</span>
          </button>
          
          <button
            onClick={logout}
            className="w-full flex items-center space-x-3 px-4 py-2 rounded-lg text-left text-red-600 hover:bg-red-50 transition-colors"
          >
            <ArrowRightOnRectangleIcon className="w-4 h-4" />
            <span className="text-xs lg:text-sm">Logout</span>
          </button>
        </div>
      </div>
      </div>
    </>
  );
};

export default Sidebar;