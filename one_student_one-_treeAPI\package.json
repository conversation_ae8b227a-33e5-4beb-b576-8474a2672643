{"name": "one-student-one-tree-api", "version": "1.0.0", "description": "Backend API for One Student One Tree initiative - R.M.K Engineering College", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "setup": "node setup.js", "setup:db": "node scripts/setup-database.js", "test:api": "node test-api.js", "migrate": "node dist/scripts/migrate.js", "seed": "node dist/scripts/seed.js"}, "keywords": ["nodejs", "typescript", "express", "postgresql", "tree-monitoring", "education"], "author": "R.M.K Engineering College", "license": "MIT", "dependencies": {"@emailjs/browser": "^4.4.1", "@types/bcrypt": "^6.0.0", "@types/helmet": "^0.0.48", "@types/react-router-dom": "^5.3.3", "axios": "^1.11.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "form-data": "^4.0.4", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.1", "pg": "^8.11.3", "rate-limiter-flexible": "^4.0.1", "react-router-dom": "^7.7.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "zod": "^4.0.14"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.13", "@types/node": "^20.10.5", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.10.9", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}