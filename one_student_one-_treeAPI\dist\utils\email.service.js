"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.emailService = void 0;
const nodemailer_1 = __importDefault(require("nodemailer"));
class EmailService {
    constructor() {
        this.transporter = null;
        this.isConfigured = false;
        this.initializeTransporter();
    }
    initializeTransporter() {
        try {
            const emailUser = process.env.EMAIL_USER || process.env.SMTP_USER || '';
            const emailPass = process.env.EMAIL_PASS || process.env.SMTP_PASSWORD || '';
            console.log('🔧 Initializing email service...');
            console.log('   EMAIL_USER:', emailUser ? `${emailUser.substring(0, 5)}***` : 'Missing');
            console.log('   EMAIL_PASS:', emailPass ? 'Set' : 'Missing');
            const config = {
                host: process.env.SMTP_HOST || 'smtp.gmail.com',
                port: parseInt(process.env.SMTP_PORT || '587'),
                secure: process.env.SMTP_SECURE === 'true',
                auth: {
                    user: emailUser,
                    pass: emailPass
                }
            };
            if (config.auth.user && config.auth.pass) {
                this.transporter = nodemailer_1.default.createTransport(config);
                this.transporter.verify((error, success) => {
                    if (error) {
                        console.error('❌ Gmail SMTP connection failed:', error.message);
                        this.isConfigured = false;
                    }
                    else {
                        console.log('✅ Gmail SMTP email service configured and verified');
                        this.isConfigured = true;
                    }
                });
                this.isConfigured = true;
                console.log('✅ Email service initialized');
            }
            else {
                console.log('⚠️  Email service not configured (missing SMTP credentials)');
                console.log('   Required: EMAIL_USER and EMAIL_PASS environment variables');
            }
        }
        catch (error) {
            console.error('❌ Failed to initialize email service:', error);
        }
    }
    async sendEmail(options) {
        try {
            if (!this.isConfigured || !this.transporter) {
                console.log('📧 Email service not configured - attempting to send anyway...');
                console.log('   To:', options.to);
                console.log('   Subject:', options.subject);
                this.initializeTransporter();
                if (!this.transporter) {
                    console.error('❌ Email service could not be initialized');
                    return false;
                }
            }
            const mailOptions = {
                from: `"One Student One Tree" <${process.env.FROM_EMAIL || process.env.EMAIL_USER || process.env.SMTP_USER}>`,
                to: options.to,
                subject: options.subject,
                html: options.html,
                text: options.text,
                headers: {
                    'X-Priority': '1',
                    'X-MSMail-Priority': 'High',
                    'Importance': 'high'
                }
            };
            const result = await this.transporter.sendMail(mailOptions);
            console.log('✅ Email sent successfully to:', options.to);
            console.log('   Message ID:', result.messageId);
            console.log('   Response:', result.response);
            console.log('   📧 Email Details:');
            console.log('     From:', mailOptions.from);
            console.log('     Subject:', options.subject);
            console.log('     Delivery Status: Accepted by SMTP server');
            return true;
        }
        catch (error) {
            console.error('❌ Failed to send email to:', options.to);
            console.error('   Error:', error.message);
            console.error('   Code:', error.code);
            console.error('   Command:', error.command);
            if (error.code === 'EAUTH') {
                console.error('   🔐 Authentication failed - check SMTP credentials');
            }
            else if (error.code === 'ECONNECTION') {
                console.error('   🌐 Connection failed - check SMTP server settings');
            }
            else if (error.responseCode === 550) {
                console.error('   📧 Email rejected - recipient may not exist');
            }
            return false;
        }
    }
    generateWelcomeEmail(userName, userEmail, temporaryPassword, role) {
        const subject = `Welcome to One Student One Tree - ${role.charAt(0).toUpperCase() + role.slice(1)} Account Created`;
        const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .credentials { background: #e5f7f0; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981; }
          .button { display: inline-block; background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          .warning { background: #fef3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #f59e0b; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🌳 One Student One Tree</h1>
            <p>Welcome to the Tree Monitoring Initiative</p>
          </div>
          <div class="content">
            <h2>Hello ${userName}!</h2>
            <p>Your ${role} account has been successfully created in the One Student One Tree system.</p>
            
            <div class="credentials">
              <h3>🔐 Your Login Credentials:</h3>
              <p><strong>Email:</strong> ${userEmail}</p>
              <p><strong>Temporary Password:</strong> <code>${temporaryPassword}</code></p>
              <p><strong>Role:</strong> ${role.charAt(0).toUpperCase() + role.slice(1)}</p>
            </div>

            <div class="warning">
              <h4>⚠️ Important Security Notice:</h4>
              <p>Please change your password immediately after your first login for security purposes.</p>
            </div>

            <p>You can now access the system using the link below:</p>
            <a href="${process.env.FRONTEND_URL || 'http://localhost:5174'}" class="button">Login to System</a>

            <h3>🌱 About One Student One Tree:</h3>
            <p>This initiative aims to promote environmental awareness and responsibility among students by assigning each student a tree to monitor and care for throughout their academic journey.</p>

            <h3>📞 Need Help?</h3>
            <p>If you have any questions or need assistance, please contact your system administrator.</p>
          </div>
          <div class="footer">
            <p>© 2025 One Student One Tree Initiative<br>
            This is an automated message, please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
        const text = `
Welcome to One Student One Tree!

Hello ${userName},

Your ${role} account has been successfully created.

Login Credentials:
- Email: ${userEmail}
- Temporary Password: ${temporaryPassword}
- Role: ${role.charAt(0).toUpperCase() + role.slice(1)}

Please change your password after your first login.

Access the system at: ${process.env.FRONTEND_URL || 'http://localhost:5174'}

If you need help, contact your system administrator.

© 2025 One Student One Tree Initiative
    `;
        return { to: userEmail, subject, html, text };
    }
    generatePasswordResetEmail(userName, userEmail, resetToken) {
        const subject = 'Password Reset - One Student One Tree';
        const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/reset-password?token=${resetToken}`;
        const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .button { display: inline-block; background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          .warning { background: #fef3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #f59e0b; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🌳 One Student One Tree</h1>
            <p>Password Reset Request</p>
          </div>
          <div class="content">
            <h2>Hello ${userName}!</h2>
            <p>We received a request to reset your password for your One Student One Tree account.</p>
            
            <p>Click the button below to reset your password:</p>
            <a href="${resetUrl}" class="button">Reset Password</a>

            <div class="warning">
              <h4>⚠️ Security Notice:</h4>
              <p>This link will expire in 1 hour for security purposes.</p>
              <p>If you didn't request this password reset, please ignore this email.</p>
            </div>

            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p><code>${resetUrl}</code></p>
          </div>
          <div class="footer">
            <p>© 2025 One Student One Tree Initiative<br>
            This is an automated message, please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
        const text = `
Password Reset - One Student One Tree

Hello ${userName},

We received a request to reset your password.

Reset your password: ${resetUrl}

This link will expire in 1 hour.

If you didn't request this, please ignore this email.

© 2025 One Student One Tree Initiative
    `;
        return { to: userEmail, subject, html, text };
    }
    generateApprovalNotificationEmail(approverName, approverEmail, requestorName, requestedRole, workflowId) {
        const subject = `Approval Required - ${requestedRole.charAt(0).toUpperCase() + requestedRole.slice(1)} Registration Request`;
        const approvalUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/dashboard?tab=approvals&workflow=${workflowId}`;
        const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          .container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
          .header { background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 30px; text-align: center; }
          .content { padding: 30px; background: #f9fafb; }
          .button { display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { background: #374151; color: #d1d5db; padding: 20px; text-align: center; font-size: 14px; }
          .urgent { background: #fef3c7; padding: 15px; border-radius: 5px; border-left: 4px solid #f59e0b; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🌳 One Student One Tree</h1>
            <p>Registration Approval Required</p>
          </div>
          <div class="content">
            <h2>Hello ${approverName}!</h2>
            <p>A new registration request requires your approval in the One Student One Tree system.</p>

            <div class="urgent">
              <h3>📋 Request Details:</h3>
              <p><strong>Applicant:</strong> ${requestorName}</p>
              <p><strong>Requested Role:</strong> ${requestedRole.charAt(0).toUpperCase() + requestedRole.slice(1)}</p>
              <p><strong>Status:</strong> Pending Your Approval</p>
            </div>

            <p>Please review and process this request at your earliest convenience:</p>
            <a href="${approvalUrl}" class="button">Review Request</a>

            <h3>⏰ Action Required:</h3>
            <p>This request is waiting for your approval to proceed to the next stage. Please log in to the system to review the applicant's details and make your decision.</p>

            <h3>📞 Need Help?</h3>
            <p>If you have any questions about this request, please contact your system administrator.</p>
          </div>
          <div class="footer">
            <p>© 2025 One Student One Tree Initiative<br>
            This is an automated message, please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
        const text = `
Approval Required - One Student One Tree

Hello ${approverName},

A new registration request requires your approval.

Request Details:
- Applicant: ${requestorName}
- Requested Role: ${requestedRole.charAt(0).toUpperCase() + requestedRole.slice(1)}
- Status: Pending Your Approval

Please review this request: ${approvalUrl}

© 2025 One Student One Tree Initiative
    `;
        return { to: approverEmail, subject, html, text };
    }
    generatePrincipalInvitationEmail(principalName, principalEmail, collegeName, invitationToken, temporaryPassword) {
        const subject = `Invitation to Join One Student One Tree Platform - ${collegeName}`;
        const invitationUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/invitation/${invitationToken}`;
        const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          .container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
          .header { background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 30px; text-align: center; }
          .content { padding: 30px; background: #f9fafb; }
          .button { display: inline-block; background: #10b981; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; font-size: 16px; }
          .footer { background: #374151; color: #d1d5db; padding: 20px; text-align: center; font-size: 14px; }
          .features { background: #e5f7f0; padding: 20px; border-radius: 8px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🌳 One Student One Tree</h1>
            <p>Welcome to the Tree Monitoring Initiative</p>
          </div>
          <div class="content">
            <h2>Welcome to One Student One Tree!</h2>

            <p>Dear ${principalName},</p>

            <p>You have been invited to join the One Student One Tree platform as the Principal of <strong>${collegeName}</strong>.</p>

            ${temporaryPassword ? `
            <div style="background: #fef3c7; border: 2px solid #f59e0b; border-radius: 8px; padding: 20px; margin: 20px 0;">
              <h3 style="color: #92400e; margin-top: 0;">🔐 Your Login Credentials</h3>
              <p style="color: #92400e; margin: 10px 0;"><strong>Email:</strong> ${principalEmail}</p>
              <p style="color: #92400e; margin: 10px 0;"><strong>Temporary Password:</strong> <code style="background: #fbbf24; padding: 4px 8px; border-radius: 4px; font-weight: bold;">${temporaryPassword}</code></p>
              <p style="color: #92400e; font-size: 14px; margin-bottom: 0;">⚠️ Please change your password after your first login for security.</p>
            </div>
            ` : ''}

            <div class="features">
              <h3>🌱 Platform Features:</h3>
              <ul>
                <li>Assign trees to students for monitoring and care</li>
                <li>Track tree growth and health progress</li>
                <li>Manage staff, departments, and student registrations</li>
                <li>Generate comprehensive reports on environmental impact</li>
              </ul>

              <h3>👨‍💼 As Principal, you can:</h3>
              <ul>
                <li>Manage your college's profile and information</li>
                <li>Invite and manage staff members and HODs</li>
                <li>Oversee student registrations and tree assignments</li>
                <li>Access detailed reports and analytics</li>
              </ul>
            </div>

            <div style="text-align: center;">
              <a href="${invitationUrl}" class="button">Accept Invitation & Register</a>
            </div>

            <p style="color: #6b7280; font-size: 14px;">
              This invitation link will expire in 7 days. If you have any questions, please contact our support team.
            </p>

            <p style="color: #6b7280; font-size: 14px;">
              If you cannot click the button above, copy and paste this link into your browser:<br>
              <code>${invitationUrl}</code>
            </p>
          </div>
          <div class="footer">
            <p>© 2025 One Student One Tree Initiative<br>
            This is an automated message, please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
        const text = `
Welcome to One Student One Tree!

Dear ${principalName},

You have been invited to join the One Student One Tree platform as the Principal of ${collegeName}.

${temporaryPassword ? `
LOGIN CREDENTIALS:
- Email: ${principalEmail}
- Temporary Password: ${temporaryPassword}

⚠️ Please change your password after your first login for security.
` : ''}

Accept your invitation: ${invitationUrl}

This invitation link will expire in 7 days.

© 2025 One Student One Tree Initiative
    `;
        return { to: principalEmail, subject, html, text };
    }
    generateStaffHODInvitationEmail(staffName, staffEmail, role, departmentName, principalName, invitationToken, temporaryPassword) {
        const subject = `Invitation to Join One Student One Tree Platform - ${role.toUpperCase()} Position`;
        const invitationUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/invitation/${invitationToken}`;
        const roleTitle = role === 'hod' ? 'Head of Department' : 'Staff Member';
        const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          .container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
          .header { background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 30px; text-align: center; }
          .content { padding: 30px; background: #f9fafb; }
          .button { display: inline-block; background: #10b981; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; font-size: 16px; }
          .footer { background: #374151; color: #d1d5db; padding: 20px; text-align: center; font-size: 14px; }
          .features { background: #e5f7f0; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .role-badge { background: #3b82f6; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🌳 One Student One Tree</h1>
            <p>Welcome to the Tree Monitoring Initiative</p>
          </div>
          <div class="content">
            <h2>Welcome to One Student One Tree!</h2>

            <p>Dear ${staffName},</p>

            <p>You have been invited by <strong>${principalName}</strong> to join the One Student One Tree platform as a <span class="role-badge">${roleTitle}</span> in the <strong>${departmentName}</strong> department.</p>

            ${temporaryPassword ? `
            <div style="background: #fef3c7; border: 2px solid #f59e0b; border-radius: 8px; padding: 20px; margin: 20px 0;">
              <h3 style="color: #92400e; margin-top: 0;">🔐 Your Login Credentials</h3>
              <p style="color: #92400e; margin: 10px 0;"><strong>Email:</strong> ${staffEmail}</p>
              <p style="color: #92400e; margin: 10px 0;"><strong>Temporary Password:</strong> <code style="background: #fbbf24; padding: 4px 8px; border-radius: 4px; font-weight: bold;">${temporaryPassword}</code></p>
              <p style="color: #92400e; font-size: 14px; margin-bottom: 0;">⚠️ Please change your password after your first login for security.</p>
            </div>
            ` : ''}

            <div class="features">
              <h3>🌱 Your Role & Responsibilities:</h3>
              ${role === 'hod' ? `
                <ul>
                  <li><strong>Department Leadership:</strong> Oversee tree monitoring activities in your department</li>
                  <li><strong>Staff Management:</strong> Invite and manage staff members in your department</li>
                  <li><strong>Student Oversight:</strong> Monitor student registrations and tree assignments</li>
                  <li><strong>Progress Tracking:</strong> Review department-wide tree care progress</li>
                  <li><strong>Reporting:</strong> Generate and review departmental reports</li>
                </ul>
              ` : `
                <ul>
                  <li><strong>Student Management:</strong> Upload and manage student information</li>
                  <li><strong>Registration Approval:</strong> Review and approve student registration requests</li>
                  <li><strong>Tree Assignment:</strong> Help assign trees to students for monitoring</li>
                  <li><strong>Progress Monitoring:</strong> Track student tree care activities</li>
                  <li><strong>Support & Guidance:</strong> Assist students with tree monitoring tasks</li>
                </ul>
              `}

              <h3>🎯 Platform Benefits:</h3>
              <ul>
                <li>Streamlined student and tree management system</li>
                <li>Real-time progress tracking and reporting</li>
                <li>Automated notifications and reminders</li>
                <li>Comprehensive analytics and insights</li>
                <li>Environmental impact measurement</li>
              </ul>
            </div>

            <div style="text-align: center;">
              <a href="${invitationUrl}" class="button">Accept Invitation & Register</a>
            </div>

            <p style="color: #6b7280; font-size: 14px;">
              This invitation link will expire in 7 days. If you have any questions, please contact your principal or our support team.
            </p>

            <p style="color: #6b7280; font-size: 14px;">
              If you cannot click the button above, copy and paste this link into your browser:<br>
              <code>${invitationUrl}</code>
            </p>
          </div>
          <div class="footer">
            <p>© 2025 One Student One Tree Initiative<br>
            This invitation was sent by ${principalName}<br>
            This is an automated message, please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
        const text = `
Welcome to One Student One Tree!

Dear ${staffName},

You have been invited by ${principalName} to join the One Student One Tree platform as a ${roleTitle} in the ${departmentName} department.

${temporaryPassword ? `
LOGIN CREDENTIALS:
- Email: ${staffEmail}
- Temporary Password: ${temporaryPassword}

⚠️ Please change your password after your first login for security.
` : ''}

Accept your invitation: ${invitationUrl}

This invitation link will expire in 7 days.

© 2025 One Student One Tree Initiative
    `;
        return { to: staffEmail, subject, html, text };
    }
    generateStudentInvitationEmail(studentName, studentEmail, rollNumber, year, section, departmentName, staffName, invitationToken, temporaryPassword) {
        const subject = `Welcome to One Student One Tree - Registration Invitation`;
        const invitationUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/invitation/${invitationToken}`;
        const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          .container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
          .header { background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 30px; text-align: center; }
          .content { padding: 30px; background: #f9fafb; }
          .button { display: inline-block; background: #10b981; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; font-size: 16px; }
          .footer { background: #374151; color: #d1d5db; padding: 20px; text-align: center; font-size: 14px; }
          .features { background: #e5f7f0; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .student-info { background: #dbeafe; padding: 15px; border-radius: 8px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🌳 One Student One Tree</h1>
            <p>Your Journey to Environmental Stewardship Begins!</p>
          </div>
          <div class="content">
            <h2>Welcome to One Student One Tree Initiative!</h2>

            <p>Dear ${studentName},</p>

            <p>You have been invited by <strong>${staffName}</strong> to join the One Student One Tree platform - an innovative environmental initiative where every student gets assigned a tree to monitor and care for!</p>

            ${temporaryPassword ? `
            <div style="background: #fef3c7; border: 2px solid #f59e0b; border-radius: 8px; padding: 20px; margin: 20px 0;">
              <h3 style="color: #92400e; margin-top: 0;">🔐 Your Login Credentials</h3>
              <p style="color: #92400e; margin: 10px 0;"><strong>Email:</strong> ${studentEmail}</p>
              <p style="color: #92400e; margin: 10px 0;"><strong>Temporary Password:</strong> <code style="background: #fbbf24; padding: 4px 8px; border-radius: 4px; font-weight: bold;">${temporaryPassword}</code></p>
              <p style="color: #92400e; font-size: 14px; margin-bottom: 0;">⚠️ Please change your password after your first login for security.</p>
            </div>
            ` : ''}

            <div class="student-info">
              <h3>📋 Your Student Information:</h3>
              <ul>
                <li><strong>Name:</strong> ${studentName}</li>
                <li><strong>Roll Number:</strong> ${rollNumber}</li>
                <li><strong>Year:</strong> ${year}${section ? ` - Section ${section}` : ''}</li>
                <li><strong>Department:</strong> ${departmentName}</li>
              </ul>
            </div>

            <div class="features">
              <h3>🌱 What You'll Do:</h3>
              <ul>
                <li><strong>Tree Assignment:</strong> Get your own tree to monitor and care for</li>
                <li><strong>Progress Tracking:</strong> Record your tree's growth and health regularly</li>
                <li><strong>Photo Documentation:</strong> Upload photos showing your tree's progress</li>
                <li><strong>Environmental Impact:</strong> Contribute to a greener campus and planet</li>
                <li><strong>Learning Experience:</strong> Gain hands-on knowledge about environmental conservation</li>
              </ul>

              <h3>🎯 Platform Features:</h3>
              <ul>
                <li>Easy-to-use mobile-friendly interface</li>
                <li>Progress tracking and milestone achievements</li>
                <li>Community features to connect with other students</li>
                <li>Educational resources about tree care</li>
                <li>Recognition and certificates for active participation</li>
              </ul>
            </div>

            <div style="text-align: center;">
              <a href="${invitationUrl}" class="button">Register & Get Your Tree!</a>
            </div>

            <p style="color: #6b7280; font-size: 14px;">
              This invitation link will expire in 14 days. Don't miss out on this amazing opportunity to make a positive environmental impact!
            </p>

            <p style="color: #6b7280; font-size: 14px;">
              If you cannot click the button above, copy and paste this link into your browser:<br>
              <code>${invitationUrl}</code>
            </p>

            <p style="margin-top: 30px;">
              <strong>Questions?</strong> Contact your staff member ${staffName} or our support team for assistance.
            </p>
          </div>
          <div class="footer">
            <p>© 2025 One Student One Tree Initiative<br>
            This invitation was sent by ${staffName}<br>
            Together, we're growing a greener future! 🌱</p>
          </div>
        </div>
      </body>
      </html>
    `;
        const text = `
Welcome to One Student One Tree Initiative!

Dear ${studentName},

You have been invited by ${staffName} to join the One Student One Tree platform.

Your Information:
- Name: ${studentName}
- Roll Number: ${rollNumber}
- Year: ${year}${section ? ` - Section ${section}` : ''}
- Department: ${departmentName}

${temporaryPassword ? `
LOGIN CREDENTIALS:
- Email: ${studentEmail}
- Temporary Password: ${temporaryPassword}

⚠️ Please change your password after your first login for security.
` : ''}

Register and get your tree: ${invitationUrl}

This invitation link will expire in 14 days.

© 2025 One Student One Tree Initiative
    `;
        return { to: studentEmail, subject, html, text };
    }
}
exports.emailService = new EmailService();
//# sourceMappingURL=email.service.js.map