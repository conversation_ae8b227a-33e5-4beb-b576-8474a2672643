import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Building, Mail, Phone, Globe, User, UserPlus, Upload } from 'lucide-react';
import { College, User as UserType } from '../../types';
import { ApiService } from '../../services/api';
import { useToast } from '../UI/Toast';
import BulkCollegeUpload from '../BulkUpload/BulkCollegeUpload';

const CollegeManagement: React.FC = () => {
  const toast = useToast();
  const [colleges, setColleges] = useState<College[]>([]);
  const [users, setUsers] = useState<UserType[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedCollege, setSelectedCollege] = useState<College | null>(null);
  const [loading, setLoading] = useState(true);
  const [showBulkUpload, setShowBulkUpload] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [collegesData, usersData] = await Promise.all([
        ApiService.getColleges(),
        ApiService.getUsers()
      ]);

      setColleges(collegesData || []);
      setUsers(usersData || []);
    } catch (error) {
      console.error('Failed to load data:', error);
      toast.error('Failed to load data', 'Please try refreshing the page');
      // Set empty arrays on error to prevent undefined issues
      setColleges([]);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };



  const getPrincipalName = (college: College) => {
    // First check if principalName is already provided by backend
    if (college.principalName) {
      return college.principalName;
    }

    // Otherwise look up by ID
    const principalId = college.principalId || college.principal_id;
    if (!principalId) return 'Not Assigned';

    const principal = users.find(u => u.id === principalId);
    return principal?.name || 'Not Assigned';
  };

  const handleAddCollege = () => {
    setSelectedCollege(null);
    setShowAddForm(true);
  };

  const handleEditCollege = (college: College) => {
    setSelectedCollege(college);
    setShowAddForm(true);
  };

  const handleDeleteCollege = async (collegeId: string) => {
    if (window.confirm('Are you sure you want to delete this college?')) {
      try {
        await ApiService.deleteCollege(collegeId);
        await loadData();
        toast.success('College deleted', 'College has been successfully removed');
      } catch (error) {
        console.error('Failed to delete college:', error);
        toast.error('Failed to delete college', 'Please try again or check your permissions');
      }
    }
  };

  const handleInvitePrincipal = async (college: College) => {
    const email = prompt('Enter the email address of the principal to invite:');
    if (!email) return;

    if (!email.includes('@') || !email.includes('.')) {
      toast.error('Invalid email', 'Please enter a valid email address.');
      return;
    }

    try {
      await ApiService.createInvitation({
        email: email,
        role: 'principal',
        college_id: college.id,
        department_id: null
      });
      toast.success('Invitation sent!', `Invitation sent successfully to ${email} for ${college.name}!`);
    } catch (error) {
      console.error('Failed to send invitation:', error);
      toast.error('Failed to send invitation', 'Please try again or check your permissions');
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 lg:p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-xl lg:text-2xl font-bold text-gray-900">College Management</h1>
          <p className="text-sm lg:text-base text-gray-600">Manage colleges and their administrative details</p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={() => setShowBulkUpload(true)}
            className="bg-blue-600 text-white px-3 lg:px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <Upload className="w-4 h-4" />
            <span className="hidden sm:inline">Bulk Upload</span>
            <span className="sm:hidden">Bulk</span>
          </button>
          <button
            onClick={handleAddCollege}
            className="bg-green-600 text-white px-3 lg:px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span className="hidden sm:inline">Add College</span>
            <span className="sm:hidden">Add</span>
          </button>
        </div>
      </div>

      {/* College Cards Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
        {colleges.length > 0 ? colleges.map((college) => (
          <div key={college.id} className="bg-white p-4 lg:p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 lg:w-12 h-10 lg:h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Building className="w-5 lg:w-6 h-5 lg:h-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-sm lg:text-base font-semibold text-gray-900 line-clamp-2">{college.name}</h3>
                  <p className="text-sm text-gray-500">Est. {college.established}</p>
                </div>
              </div>
              <div className="flex space-x-1 flex-shrink-0">
                <button
                  onClick={() => handleInvitePrincipal(college)}
                  className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                  title="Invite Principal"
                >
                  <UserPlus className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleEditCollege(college)}
                  className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  title="Edit College"
                >
                  <Edit className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDeleteCollege(college.id)}
                  className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  title="Delete College"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-start space-x-2 text-xs lg:text-sm text-gray-600">
                <Mail className="w-4 h-4" />
                <span className="break-all">{college.email}</span>
              </div>
              <div className="flex items-center space-x-2 text-xs lg:text-sm text-gray-600">
                <Phone className="w-4 h-4" />
                <span>{college.phone}</span>
              </div>
              <div className="flex items-start space-x-2 text-xs lg:text-sm text-gray-600">
                <Globe className="w-4 h-4" />
                <span className="break-all">{college.website}</span>
              </div>
              <div className="flex items-start space-x-2 text-xs lg:text-sm text-gray-600">
                <User className="w-4 h-4" />
                <span className="line-clamp-1">Principal: {getPrincipalName(college)}</span>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-gray-100">
              <div className="flex justify-between text-xs lg:text-sm">
                <span className="text-gray-500">Departments</span>
                <span className="font-medium text-gray-900">{college.departments?.length || 0}</span>
              </div>
              <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium mt-2 ${
                college.status === 'active' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {college.status}
              </div>
            </div>
          </div>
        )) : (
          <div className="col-span-full text-center py-12">
            <Building className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No colleges found</h3>
            <p className="text-gray-500 mb-4">Get started by adding your first college.</p>
            <button
              onClick={handleAddCollege}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              Add College
            </button>
          </div>
        )}
      </div>

      {/* Add/Edit Form Modal */}
      {showAddForm && (
        <CollegeForm
          college={selectedCollege}
          onClose={() => setShowAddForm(false)}
          onSave={async (college) => {
            try {
              if (selectedCollege) {
                await ApiService.updateCollege(college.id, college);
                toast.success('College updated', 'College information has been successfully updated');
              } else {
                await ApiService.createCollege(college);
                toast.success('College created', 'New college has been successfully added');
              }
              await loadData();
              setShowAddForm(false);
            } catch (error: any) {
              console.error('Failed to save college:', error);

              // Show specific validation errors if available
              if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
                const errorMessages = error.response.data.errors.map((err: any) =>
                  `${err.field}: ${err.message}`
                ).join('\n');
                toast.error('Validation Error', errorMessages);
              } else if (error.response?.data?.message) {
                toast.error('Failed to save college', error.response.data.message);
              } else {
                toast.error('Failed to save college', 'Please check your input and try again');
              }
            }
          }}
        />
      )}

      {/* Bulk Upload Modal */}
      {showBulkUpload && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-bold text-gray-900">Bulk College Upload</h2>
                <button
                  onClick={() => setShowBulkUpload(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            <div className="p-6">
              <BulkCollegeUpload />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// College Form Component
interface CollegeFormProps {
  college: College | null;
  onClose: () => void;
  onSave: (college: College) => Promise<void>;
}

const CollegeForm: React.FC<CollegeFormProps> = ({ college, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    name: college?.name || '',
    address: college?.address || '',
    phone: college?.phone || '',
    email: college?.email || '',
    website: college?.website || '',
    established: college?.established || '',
    status: college?.status || 'active',
    principalName: college?.principalName || '',
    principalEmail: college?.principalEmail || '',
    principalPhone: college?.principalPhone || ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const collegeData = {
      name: formData.name,
      address: formData.address,
      phone: formData.phone,
      email: formData.email,
      website: formData.website,
      established: formData.established,
      status: formData.status,
      principalName: formData.principalName,
      principalEmail: formData.principalEmail,
      principalPhone: formData.principalPhone
    };

    // If editing existing college, include the ID
    if (college?.id) {
      (collegeData as any).id = college.id;
    }

    await onSave(collegeData as College);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-md w-full p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">
          {college ? 'Edit College' : 'Add New College'}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">College Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
            <textarea
              value={formData.address}
              onChange={(e) => setFormData({...formData, address: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              rows={3}
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone <span className="text-red-500">*</span>
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => {
                  // Allow only digits, spaces, hyphens, parentheses, and plus sign
                  const cleanValue = e.target.value.replace(/[^+\d\s\-\(\)]/g, '');
                  setFormData({...formData, phone: cleanValue});
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="+91-98765-43210"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Only digits, spaces, hyphens, and parentheses allowed (10-20 characters)
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Established Year (Optional)</label>
              <input
                type="text"
                placeholder="e.g., 1985"
                value={formData.established}
                onChange={(e) => {
                  // Allow only 4 digits for year
                  const cleanValue = e.target.value.replace(/[^\d]/g, '').slice(0, 4);
                  setFormData({...formData, established: cleanValue});
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                maxLength={4}
              />
              <p className="text-xs text-gray-500 mt-1">
                Enter 4-digit year (e.g., 1985, 2024)
              </p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Website (Optional)</label>
            <input
              type="url"
              placeholder="https://college.edu"
              value={formData.website}
              onChange={(e) => setFormData({...formData, website: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Principal Name (Optional)</label>
              <input
                type="text"
                value={formData.principalName}
                onChange={(e) => setFormData({...formData, principalName: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Dr. John Smith"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Principal Email (Optional)</label>
              <input
                type="email"
                value={formData.principalEmail}
                onChange={(e) => setFormData({...formData, principalEmail: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Principal Phone (Optional)</label>
            <input
              type="tel"
              value={formData.principalPhone}
              onChange={(e) => {
                // Allow only digits, spaces, hyphens, parentheses, and plus sign
                const cleanValue = e.target.value.replace(/[^+\d\s\-\(\)]/g, '');
                setFormData({...formData, principalPhone: cleanValue});
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="+91-98765-43210"
            />
            <p className="text-xs text-gray-500 mt-1">
              📧 If you provide principal details, an invitation email will be sent automatically after creating the college.
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({...formData, status: e.target.value as 'active' | 'inactive'})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
            >
              {college ? 'Update' : 'Create'} College
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CollegeManagement;