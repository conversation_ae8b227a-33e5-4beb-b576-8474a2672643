"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStudentUploadTemplate = exports.uploadStudents = exports.getStaffHODUploadTemplate = exports.uploadStaffAndHODs = exports.getBulkUploadTemplate = exports.uploadCollegesWithPrincipals = exports.csvUpload = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const csv_parser_1 = __importDefault(require("csv-parser"));
const uuid_1 = require("uuid");
const college_repository_1 = require("../../college/repositories/college.repository");
const user_repository_1 = require("../../user/repositories/user.repository");
const invitation_repository_1 = require("../../invitation/repositories/invitation.repository");
const department_repository_1 = require("../../department/repositories/department.repository");
const email_service_1 = require("../../../utils/email.service");
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path_1.default.join(__dirname, '../../../../uploads/csv');
        if (!fs_1.default.existsSync(uploadDir)) {
            fs_1.default.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const uniqueName = `${(0, uuid_1.v4)()}-${Date.now()}${path_1.default.extname(file.originalname)}`;
        cb(null, uniqueName);
    }
});
const csvFileFilter = (req, file, cb) => {
    if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
        cb(null, true);
    }
    else {
        cb(new Error('Only CSV files are allowed!'));
    }
};
exports.csvUpload = (0, multer_1.default)({
    storage,
    fileFilter: csvFileFilter,
    limits: {
        fileSize: 10 * 1024 * 1024,
    }
});
const uploadCollegesWithPrincipals = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required',
            });
            return;
        }
        if (req.user.role !== 'admin') {
            res.status(403).json({
                success: false,
                message: 'Only administrators can upload colleges',
            });
            return;
        }
        if (!req.file) {
            res.status(400).json({
                success: false,
                message: 'No CSV file provided',
            });
            return;
        }
        const results = [];
        const errors = [];
        const successfulUploads = [];
        const invitationsSent = [];
        await new Promise((resolve, reject) => {
            fs_1.default.createReadStream(req.file.path)
                .pipe((0, csv_parser_1.default)())
                .on('data', (data) => {
                if (!data.collegeName || !data.collegeAddress || !data.collegePhone ||
                    !data.collegeEmail || !data.principalName || !data.principalEmail ||
                    !data.principalPhone) {
                    errors.push(`Row with college "${data.collegeName || 'Unknown'}" is missing required fields`);
                    return;
                }
                results.push({
                    collegeName: data.collegeName.trim(),
                    collegeAddress: data.collegeAddress.trim(),
                    collegePhone: data.collegePhone.trim(),
                    collegeEmail: data.collegeEmail.trim().toLowerCase(),
                    collegeWebsite: data.collegeWebsite?.trim() || '',
                    collegeEstablished: data.collegeEstablished?.trim() || '',
                    principalName: data.principalName.trim(),
                    principalEmail: data.principalEmail.trim().toLowerCase(),
                    principalPhone: data.principalPhone.trim()
                });
            })
                .on('end', resolve)
                .on('error', reject);
        });
        for (const item of results) {
            try {
                const existingCollege = await college_repository_1.collegeRepository.findByEmail(item.collegeEmail);
                if (existingCollege) {
                    errors.push(`College with email ${item.collegeEmail} already exists`);
                    continue;
                }
                const existingUser = await user_repository_1.userRepository.findByEmail(item.principalEmail);
                if (existingUser) {
                    errors.push(`Principal with email ${item.principalEmail} already exists`);
                    continue;
                }
                const collegeData = {
                    name: item.collegeName,
                    address: item.collegeAddress,
                    phone: item.collegePhone,
                    email: item.collegeEmail,
                    website: item.collegeWebsite || undefined,
                    established: item.collegeEstablished || undefined,
                    status: 'active'
                };
                const newCollege = await college_repository_1.collegeRepository.createCollege(collegeData);
                const invitationToken = 'inv-' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
                const expiresAt = new Date();
                expiresAt.setDate(expiresAt.getDate() + 7);
                const invitation = await invitation_repository_1.invitationRepository.createInvitation({
                    email: item.principalEmail,
                    role: 'principal',
                    sentBy: req.user.id,
                    collegeId: newCollege.id,
                    departmentId: null,
                    invitationToken,
                    expiresAt,
                    status: 'pending'
                });
                try {
                    const temporaryPassword = 'TempPassword@123';
                    const emailOptions = email_service_1.emailService.generatePrincipalInvitationEmail(item.principalName, item.principalEmail, item.collegeName, invitationToken, temporaryPassword);
                    await email_service_1.emailService.sendEmail(emailOptions);
                    invitationsSent.push({
                        principalName: item.principalName,
                        principalEmail: item.principalEmail,
                        collegeName: item.collegeName
                    });
                }
                catch (emailError) {
                    console.error('Failed to send invitation email:', emailError);
                    errors.push(`Failed to send invitation email to ${item.principalEmail}`);
                }
                successfulUploads.push({
                    college: newCollege,
                    principalName: item.principalName,
                    principalEmail: item.principalEmail,
                    invitationSent: true
                });
            }
            catch (error) {
                console.error('Error processing college-principal:', error);
                errors.push(`Failed to process ${item.collegeName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }
        try {
            fs_1.default.unlinkSync(req.file.path);
        }
        catch (cleanupError) {
            console.error('Failed to clean up uploaded file:', cleanupError);
        }
        res.status(200).json({
            success: true,
            message: 'Bulk upload completed',
            data: {
                totalProcessed: results.length,
                successfulUploads: successfulUploads.length,
                invitationsSent: invitationsSent.length,
                errors: errors.length,
                details: {
                    successful: successfulUploads,
                    invitations: invitationsSent,
                    errors: errors
                }
            }
        });
    }
    catch (error) {
        console.error('Bulk upload error:', error);
        if (req.file && fs_1.default.existsSync(req.file.path)) {
            try {
                fs_1.default.unlinkSync(req.file.path);
            }
            catch (cleanupError) {
                console.error('Failed to clean up uploaded file:', cleanupError);
            }
        }
        res.status(500).json({
            success: false,
            message: 'Failed to process bulk upload',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.uploadCollegesWithPrincipals = uploadCollegesWithPrincipals;
const getBulkUploadTemplate = async (req, res) => {
    try {
        const csvTemplate = [
            'collegeName,collegeAddress,collegePhone,collegeEmail,collegeWebsite,collegeEstablished,principalName,principalEmail,principalPhone',
            'Sample College,123 Education St, City, State 12345,******-0123,<EMAIL>,https://samplecollege.edu,1985,Dr. John Principal,<EMAIL>,******-0124',
            'Another College,456 Learning Ave, Town, State 67890,******-0125,<EMAIL>,https://anothercollege.edu,1990,Dr. Jane Principal,<EMAIL>,******-0126'
        ].join('\n');
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename="college_upload_template.csv"');
        res.send(csvTemplate);
    }
    catch (error) {
        console.error('Template generation error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to generate template',
        });
    }
};
exports.getBulkUploadTemplate = getBulkUploadTemplate;
const uploadStaffAndHODs = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required',
            });
            return;
        }
        if (req.user.role !== 'principal') {
            res.status(403).json({
                success: false,
                message: 'Only principals can upload staff and HODs',
            });
            return;
        }
        if (!req.file) {
            res.status(400).json({
                success: false,
                message: 'No CSV file provided',
            });
            return;
        }
        const results = [];
        const errors = [];
        const successfulUploads = [];
        const invitationsSent = [];
        await new Promise((resolve, reject) => {
            fs_1.default.createReadStream(req.file.path)
                .pipe((0, csv_parser_1.default)())
                .on('data', (data) => {
                if (!data.name || !data.email || !data.phone || !data.role || !data.departmentName) {
                    errors.push(`Row with name "${data.name || 'Unknown'}" is missing required fields`);
                    return;
                }
                if (!['staff', 'hod'].includes(data.role.toLowerCase())) {
                    errors.push(`Invalid role "${data.role}" for ${data.name}. Must be 'staff' or 'hod'`);
                    return;
                }
                results.push({
                    name: data.name.trim(),
                    email: data.email.trim().toLowerCase(),
                    phone: data.phone.trim(),
                    role: data.role.toLowerCase(),
                    departmentName: data.departmentName.trim(),
                    departmentCode: data.departmentCode?.trim() || '',
                    subject: data.subject?.trim() || '',
                    designation: data.designation?.trim() || ''
                });
            })
                .on('end', resolve)
                .on('error', reject);
        });
        const principalCollegeId = req.user.collegeId;
        if (!principalCollegeId) {
            res.status(400).json({
                success: false,
                message: 'Principal is not assigned to any college',
            });
            return;
        }
        for (const item of results) {
            try {
                const existingUser = await user_repository_1.userRepository.findByEmail(item.email);
                if (existingUser) {
                    errors.push(`User with email ${item.email} already exists`);
                    continue;
                }
                let department = await department_repository_1.departmentRepository.findByNameAndCollege(item.departmentName, principalCollegeId);
                if (!department) {
                    try {
                        const departmentCode = item.departmentCode || item.departmentName.substring(0, 4).toUpperCase();
                        department = await department_repository_1.departmentRepository.createDepartment({
                            name: item.departmentName,
                            code: departmentCode,
                            collegeId: principalCollegeId,
                            established: new Date().getFullYear().toString()
                        });
                    }
                    catch (deptError) {
                        if (deptError.message.includes('duplicate key')) {
                            const departmentCode = item.departmentCode || item.departmentName.substring(0, 4).toUpperCase();
                            department = await department_repository_1.departmentRepository.findByCodeAndCollege(departmentCode, principalCollegeId);
                            if (!department) {
                                throw new Error(`Department creation failed and could not find existing department: ${deptError.message}`);
                            }
                        }
                        else {
                            throw deptError;
                        }
                    }
                }
                const invitationToken = 'inv-' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
                const expiresAt = new Date();
                expiresAt.setDate(expiresAt.getDate() + 7);
                const invitation = await invitation_repository_1.invitationRepository.createInvitation({
                    email: item.email,
                    role: item.role,
                    sentBy: req.user.id,
                    collegeId: principalCollegeId,
                    departmentId: department.id,
                    invitationToken,
                    expiresAt,
                    status: 'pending'
                });
                try {
                    const temporaryPassword = 'TempPassword@123';
                    const emailOptions = email_service_1.emailService.generateStaffHODInvitationEmail(item.name, item.email, item.role, item.departmentName, req.user.name || 'Principal', invitationToken, temporaryPassword);
                    await email_service_1.emailService.sendEmail(emailOptions);
                    invitationsSent.push({
                        name: item.name,
                        email: item.email,
                        role: item.role,
                        departmentName: item.departmentName
                    });
                }
                catch (emailError) {
                    console.error('Failed to send invitation email:', emailError);
                    errors.push(`Failed to send invitation email to ${item.email}`);
                }
                successfulUploads.push({
                    name: item.name,
                    email: item.email,
                    role: item.role,
                    department: department,
                    invitationSent: true
                });
            }
            catch (error) {
                console.error('Error processing staff/HOD:', error);
                errors.push(`Failed to process ${item.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }
        try {
            fs_1.default.unlinkSync(req.file.path);
        }
        catch (cleanupError) {
            console.error('Failed to clean up uploaded file:', cleanupError);
        }
        res.status(200).json({
            success: true,
            message: 'Staff/HOD bulk upload completed',
            data: {
                totalProcessed: results.length,
                successfulUploads: successfulUploads.length,
                invitationsSent: invitationsSent.length,
                errors: errors.length,
                details: {
                    successful: successfulUploads,
                    invitations: invitationsSent,
                    errors: errors
                }
            }
        });
    }
    catch (error) {
        console.error('Staff/HOD bulk upload error:', error);
        if (req.file && fs_1.default.existsSync(req.file.path)) {
            try {
                fs_1.default.unlinkSync(req.file.path);
            }
            catch (cleanupError) {
                console.error('Failed to clean up uploaded file:', cleanupError);
            }
        }
        res.status(500).json({
            success: false,
            message: 'Failed to process staff/HOD bulk upload',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.uploadStaffAndHODs = uploadStaffAndHODs;
const getStaffHODUploadTemplate = async (req, res) => {
    try {
        const csvTemplate = [
            'name,email,phone,role,departmentName,departmentCode,subject,designation',
            'Dr. John Staff,<EMAIL>,******-0123,staff,Computer Science,CSE,Data Structures,Assistant Professor',
            'Dr. Jane HOD,<EMAIL>,******-0124,hod,Computer Science,CSE,Algorithms,Professor & HOD',
            'Prof. Mike Staff,<EMAIL>,******-0125,staff,Electronics,ECE,Digital Circuits,Associate Professor'
        ].join('\n');
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename="staff_hod_upload_template.csv"');
        res.send(csvTemplate);
    }
    catch (error) {
        console.error('Template generation error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to generate template',
        });
    }
};
exports.getStaffHODUploadTemplate = getStaffHODUploadTemplate;
const uploadStudents = async (req, res) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required',
            });
            return;
        }
        if (!['staff', 'hod'].includes(req.user.role)) {
            res.status(403).json({
                success: false,
                message: 'Only staff and HODs can upload students',
            });
            return;
        }
        if (!req.file) {
            res.status(400).json({
                success: false,
                message: 'No CSV file provided',
            });
            return;
        }
        const results = [];
        const errors = [];
        const successfulUploads = [];
        const invitationsSent = [];
        await new Promise((resolve, reject) => {
            fs_1.default.createReadStream(req.file.path)
                .pipe((0, csv_parser_1.default)())
                .on('data', (data) => {
                if (!data.name || !data.email || !data.phone || !data.rollNumber ||
                    !data.year || !data.departmentName) {
                    errors.push(`Row with student "${data.name || 'Unknown'}" is missing required fields`);
                    return;
                }
                if (!data.email.includes('@') || !data.email.includes('.')) {
                    errors.push(`Invalid email format for student ${data.name}: ${data.email}`);
                    return;
                }
                results.push({
                    name: data.name.trim(),
                    email: data.email.trim().toLowerCase(),
                    phone: data.phone.trim(),
                    rollNumber: data.rollNumber.trim().toUpperCase(),
                    year: data.year.trim(),
                    section: data.section?.trim() || '',
                    departmentName: data.departmentName.trim(),
                    departmentCode: data.departmentCode?.trim() || '',
                    guardianName: data.guardianName?.trim() || '',
                    guardianPhone: data.guardianPhone?.trim() || '',
                    address: data.address?.trim() || ''
                });
            })
                .on('end', resolve)
                .on('error', reject);
        });
        const staffCollegeId = req.user.collegeId;
        const staffDepartmentId = req.user.departmentId;
        if (!staffCollegeId) {
            res.status(400).json({
                success: false,
                message: 'Staff member is not assigned to any college',
            });
            return;
        }
        for (const item of results) {
            try {
                const existingUser = await user_repository_1.userRepository.findByEmail(item.email);
                if (existingUser) {
                    errors.push(`Student with email ${item.email} already exists`);
                    continue;
                }
                const existingStudent = await user_repository_1.userRepository.findByRollNumberAndCollege(item.rollNumber, staffCollegeId);
                if (existingStudent) {
                    errors.push(`Student with roll number ${item.rollNumber} already exists in this college`);
                    continue;
                }
                let department = await department_repository_1.departmentRepository.findByNameAndCollege(item.departmentName, staffCollegeId);
                if (!department) {
                    if (req.user.role === 'staff' && staffDepartmentId) {
                        errors.push(`Staff can only upload students for their own department. ${item.name} belongs to ${item.departmentName}`);
                        continue;
                    }
                    try {
                        const departmentCode = item.departmentCode || item.departmentName.substring(0, 4).toUpperCase();
                        department = await department_repository_1.departmentRepository.createDepartment({
                            name: item.departmentName,
                            code: departmentCode,
                            collegeId: staffCollegeId,
                            established: new Date().getFullYear().toString()
                        });
                    }
                    catch (deptError) {
                        if (deptError.message.includes('duplicate key')) {
                            const departmentCode = item.departmentCode || item.departmentName.substring(0, 4).toUpperCase();
                            department = await department_repository_1.departmentRepository.findByCodeAndCollege(departmentCode, staffCollegeId);
                            if (!department) {
                                throw new Error(`Department creation failed and could not find existing department: ${deptError.message}`);
                            }
                        }
                        else {
                            throw deptError;
                        }
                    }
                }
                if (req.user.role === 'staff' && staffDepartmentId && department.id !== staffDepartmentId) {
                    errors.push(`Staff can only upload students for their own department. ${item.name} belongs to ${item.departmentName}`);
                    continue;
                }
                const invitationToken = 'inv-' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
                const expiresAt = new Date();
                expiresAt.setDate(expiresAt.getDate() + 14);
                const invitation = await invitation_repository_1.invitationRepository.createInvitation({
                    email: item.email,
                    role: 'student',
                    sentBy: req.user.id,
                    collegeId: staffCollegeId,
                    departmentId: department.id,
                    invitationToken,
                    expiresAt,
                    status: 'pending'
                });
                try {
                    const temporaryPassword = 'TempPassword@123';
                    const emailOptions = email_service_1.emailService.generateStudentInvitationEmail(item.name, item.email, item.rollNumber, item.year, item.section, item.departmentName, req.user.name || 'Staff Member', invitationToken, temporaryPassword);
                    await email_service_1.emailService.sendEmail(emailOptions);
                    invitationsSent.push({
                        name: item.name,
                        email: item.email,
                        rollNumber: item.rollNumber,
                        year: item.year,
                        departmentName: item.departmentName
                    });
                }
                catch (emailError) {
                    console.error('Failed to send invitation email:', emailError);
                    errors.push(`Failed to send invitation email to ${item.email}`);
                }
                successfulUploads.push({
                    name: item.name,
                    email: item.email,
                    rollNumber: item.rollNumber,
                    year: item.year,
                    section: item.section,
                    department: department,
                    guardianName: item.guardianName,
                    invitationSent: true
                });
            }
            catch (error) {
                console.error('Error processing student:', error);
                errors.push(`Failed to process ${item.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }
        try {
            fs_1.default.unlinkSync(req.file.path);
        }
        catch (cleanupError) {
            console.error('Failed to clean up uploaded file:', cleanupError);
        }
        res.status(200).json({
            success: true,
            message: 'Student bulk upload completed',
            data: {
                totalProcessed: results.length,
                successfulUploads: successfulUploads.length,
                invitationsSent: invitationsSent.length,
                errors: errors.length,
                details: {
                    successful: successfulUploads,
                    invitations: invitationsSent,
                    errors: errors
                }
            }
        });
    }
    catch (error) {
        console.error('Student bulk upload error:', error);
        if (req.file && fs_1.default.existsSync(req.file.path)) {
            try {
                fs_1.default.unlinkSync(req.file.path);
            }
            catch (cleanupError) {
                console.error('Failed to clean up uploaded file:', cleanupError);
            }
        }
        res.status(500).json({
            success: false,
            message: 'Failed to process student bulk upload',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.uploadStudents = uploadStudents;
const getStudentUploadTemplate = async (req, res) => {
    try {
        const csvTemplate = [
            'name,email,phone,rollNumber,year,section,departmentName,departmentCode,guardianName,guardianPhone,address',
            'John Student,<EMAIL>,******-0123,CS21001,2021,A,Computer Science,CSE,John Guardian,******-0124,123 Student St',
            'Jane Student,<EMAIL>,******-0125,CS21002,2021,A,Computer Science,CSE,Jane Guardian,******-0126,456 Student Ave',
            'Mike Student,<EMAIL>,******-0127,EC21001,2021,B,Electronics,ECE,Mike Guardian,******-0128,789 Student Blvd'
        ].join('\n');
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename="student_upload_template.csv"');
        res.send(csvTemplate);
    }
    catch (error) {
        console.error('Template generation error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to generate template',
        });
    }
};
exports.getStudentUploadTemplate = getStudentUploadTemplate;
//# sourceMappingURL=bulk-upload.controller.js.map