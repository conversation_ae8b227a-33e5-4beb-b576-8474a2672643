import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000/api/v1';

async function testStudentLogin() {
  console.log('🧪 Testing Student Login Functionality...\n');

  try {
    // Test 1: Get a list of students to find valid credentials
    console.log('1. Getting list of students...');
    const studentsResponse = await axios.get(`${API_BASE_URL}/users?role=student`);
    const students = studentsResponse.data.data;
    
    if (students.length === 0) {
      console.log('❌ No students found in database');
      return;
    }
    
    console.log(`✅ Found ${students.length} students`);
    const testStudent = students[0];
    console.log(`📝 Test student: ${testStudent.name} (${testStudent.email})`);

    // Test 2: Try to login with the student
    console.log('\n2. Testing student login...');
    
    // Try common passwords
    const commonPasswords = ['Student@123', 'password123', 'Student@123456', 'student123'];
    let loginSuccess = false;
    let authToken = null;
    
    for (const password of commonPasswords) {
      try {
        const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
          email: testStudent.email,
          password: password
        });
        
        if (loginResponse.data.success) {
          console.log(`✅ Login successful with password: ${password}`);
          authToken = loginResponse.data.data.token;
          loginSuccess = true;
          break;
        }
      } catch (error) {
        console.log(`❌ Login failed with password: ${password}`);
      }
    }
    
    if (!loginSuccess) {
      console.log('❌ Could not login with any common password');
      return;
    }

    // Test 3: Test authenticated student endpoints
    console.log('\n3. Testing student-specific endpoints...');
    const headers = { Authorization: `Bearer ${authToken}` };
    
    // Test tree selection endpoint
    try {
      const treeSelectionResponse = await axios.get(`${API_BASE_URL}/tree-selection/my-selection`, { headers });
      console.log('✅ Tree selection endpoint working');
      console.log(`📊 Tree selection data:`, treeSelectionResponse.data.data ? 'Has tree assigned' : 'No tree assigned');
    } catch (error) {
      console.log('❌ Tree selection endpoint failed:', error.response?.data?.message || error.message);
    }

    // Test available trees endpoint
    try {
      const availableTreesResponse = await axios.get(`${API_BASE_URL}/tree-selection/available`, { headers });
      console.log('✅ Available trees endpoint working');
      console.log(`📊 Available trees: ${availableTreesResponse.data.data?.length || 0}`);
    } catch (error) {
      console.log('❌ Available trees endpoint failed:', error.response?.data?.message || error.message);
    }

    // Test dashboard data
    try {
      const dashboardResponse = await axios.get(`${API_BASE_URL}/dashboard/overview`, { headers });
      console.log('✅ Dashboard overview endpoint working');
    } catch (error) {
      console.log('❌ Dashboard overview endpoint failed:', error.response?.data?.message || error.message);
    }

    // Test 4: Test frontend accessibility
    console.log('\n4. Testing frontend accessibility...');
    try {
      const frontendResponse = await axios.get('http://localhost:5173');
      console.log('✅ Frontend is accessible');
    } catch (error) {
      console.log('❌ Frontend not accessible:', error.message);
    }

    console.log('\n🎉 Student login test completed!');
    console.log('\n📋 Test Summary:');
    console.log(`✅ Student found: ${testStudent.name}`);
    console.log(`✅ Login successful: ${loginSuccess}`);
    console.log('✅ API endpoints tested');
    console.log('✅ Frontend accessible');
    
    console.log('\n🔑 Student Test Credentials:');
    console.log(`Email: ${testStudent.email}`);
    console.log(`Password: Student@123 (or similar)`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testStudentLogin();
