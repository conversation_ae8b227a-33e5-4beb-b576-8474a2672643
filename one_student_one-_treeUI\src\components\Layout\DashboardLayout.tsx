import React, { useState } from 'react';
import { PuzzlePieceIcon } from '@heroicons/react/24/outline';
import Sidebar from './Sidebar';
import MobileHeader from './MobileHeader';
import { useAuth } from '../../hooks/useAuth';

// Dashboard Components
import AdminDashboard from '../Dashboard/AdminDashboard';
import PrincipalDashboard from '../Dashboard/PrincipalDashboard';
import HODDashboard from '../Dashboard/HODDashboard';
import StaffDashboard from '../Dashboard/StaffDashboard';
import StudentDashboard from '../Dashboard/StudentDashboard';

// Management Components
import CollegeManagement from '../Management/CollegeManagement';
import UserManagement from '../Management/UserManagement';
import StaffManagement from '../Management/StaffManagement';
import StudentManagement from '../Management/StudentManagement';
import MyStudents from '../Management/MyStudents';
import DepartmentManagement from '../Management/DepartmentManagement';
import InvitationManagement from '../Management/InvitationManagement';
import RegistrationRequests from '../Management/RegistrationRequests';
import TreeManagement from '../Management/TreeManagement';
import ContentManagement from '../Management/ContentManagement';
import DataVisualizationDashboard from '../Dashboard/DataVisualizationDashboard';
import StudentProgressMonitoring from '../Monitoring/StudentProgressMonitoring';
import SmartRecommendations from '../Features/SmartRecommendations';

// Debug Components
import ApiTest from '../Debug/ApiTest';
import DataTest from '../Debug/DataTest';
import TestPage from '../TestPage';
import RoleTestPage from '../RoleTestPage';

// Enhanced Components
import EnhancedStudentDashboard from '../Dashboard/EnhancedStudentDashboard';
import ApprovalManagement from '../Management/ApprovalManagement';
import DepartmentSummary from '../Dashboard/DepartmentSummary';

// Tree Components
import TreeSelection from '../TreeSelection/TreeSelection';
import TreeProgress from '../TreeProgress/TreeProgress';
import SmartTreeRecommendation from '../TreeRecommendation/SmartTreeRecommendation';

// Reports Components
import Reports from '../Reports/Reports';

// Settings Components
import Settings from '../Settings/Settings';

// UI Components
import ThemeToggle from '../UI/ThemeToggle';

// Mobile Components
import MobileNavigation from '../Mobile/MobileNavigation';
import MobileDashboard from '../Mobile/MobileDashboard';

// Page Components
import GuidelinesPage from '../Pages/GuidelinesPage';
import ResourcesPage from '../Pages/ResourcesPage';
import SettingsPage from '../Pages/SettingsPage';

// Bulk Upload Components
import BulkCollegeUpload from '../BulkUpload/BulkCollegeUpload';
import BulkStaffHODUpload from '../BulkUpload/BulkStaffHODUpload';
import BulkStudentUpload from '../BulkUpload/BulkStudentUpload';

const DashboardLayout: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <>
            {/* Desktop Dashboard */}
            <div className="hidden md:block">
              {user?.role === 'admin' && <AdminDashboard onNavigate={setActiveTab} />}
              {user?.role === 'principal' && <PrincipalDashboard onNavigate={setActiveTab} />}
              {user?.role === 'hod' && <HODDashboard onNavigate={setActiveTab} />}
              {user?.role === 'staff' && <StaffDashboard onNavigate={setActiveTab} />}
              {user?.role === 'student' && <EnhancedStudentDashboard onNavigate={setActiveTab} />}
              {!user?.role && <AdminDashboard onNavigate={setActiveTab} />}
            </div>
            {/* Mobile Dashboard */}
            <div className="md:hidden">
              <MobileDashboard />
            </div>
          </>
        );

      case 'colleges':
        return <CollegeManagement />;

      case 'users':
        return <UserManagement />;

      case 'reports':
        return <Reports />;

      case 'staff-management':
        return <StaffManagement />;

      case 'department-staff':
        return <StaffManagement />;

      case 'students':
        return <StudentManagement />;

      case 'department-students':
        return <StudentManagement />;

      case 'my-students':
        return <MyStudents />;

      case 'student-management':
        return <StudentManagement />;

      case 'departments':
        return <DepartmentManagement />;

      case 'invitations':
        return <InvitationManagement />;

      case 'bulk-upload':
        return <BulkCollegeUpload />;

      case 'bulk-upload-staff':
        return <BulkStaffHODUpload />;

      case 'bulk-upload-students':
        return <BulkStudentUpload />;

      case 'requests':
        return <RegistrationRequests />;

      case 'student-requests':
        return <RegistrationRequests />;

      case 'tree-management':
        return <TreeManagement />;

      case 'content-management':
        return <ContentManagement />;

      case 'data-visualization':
        return <DataVisualizationDashboard />;

      case 'student-monitoring':
        return <StudentProgressMonitoring />;

      case 'department-summary':
        return <DepartmentSummary />;

      case 'smart-recommendations':
        return <SmartRecommendations />;

      case 'tree-selection':
        return <TreeSelection />;

      case 'my-tree':
        return <TreeProgress />;

      case 'guidelines':
        return <GuidelinesPage />;

      case 'resources':
        return <ResourcesPage />;

      case 'settings':
        return <SettingsPage />;

      case 'api-test':
        return <ApiTest />;

      case 'data-test':
        return <DataTest />;

      case 'test-page':
        return <TestPage />;

      case 'role-test':
        return <RoleTestPage />;

      case 'approvals':
        return <ApprovalManagement />;

      default:
        return <AdminDashboard />;
    }
  };

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900 overflow-hidden">
      {/* Desktop Sidebar */}
      <div className="hidden md:block">
        <Sidebar
          activeTab={activeTab}
          onTabChange={setActiveTab}
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
        />
      </div>

      {/* Mobile Navigation */}
      <div className="md:hidden">
        <MobileNavigation
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Desktop Header - Only show on larger screens */}
        <header className="hidden lg:block bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {activeTab.charAt(0).toUpperCase() + activeTab.slice(1).replace('-', ' ')}
            </h1>
            <div className="flex items-center space-x-4">
              <ThemeToggle />
            </div>
          </div>
        </header>

        <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900">
          <div className="min-h-full">
            {renderContent()}
          </div>
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;